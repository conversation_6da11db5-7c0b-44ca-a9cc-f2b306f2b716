
## Metrics

### OLD

new_technical_debt,blocker_violations,bugs,classes,code_smells,cognitive_complexity,comment_lines,comment_lines_data,comment_lines_density,class_complexity,file_complexity,function_complexity,complexity_in_classes,complexity_in_functions,branch_coverage,new_branch_coverage,conditions_to_cover,new_conditions_to_cover,confirmed_issues,coverage,new_coverage,critical_violations,complexity,last_commit_date,new_development_cost,directories,duplicated_blocks,new_duplicated_blocks,duplicated_files,duplicated_lines,duplicated_lines_density,new_duplicated_lines_density,new_duplicated_lines,duplications_data,effort_to_reach_maintainability_rating_a,executable_lines_data,false_positive_issues,file_complexity_distribution,files,function_complexity_distribution,functions,generated_lines,generated_ncloc,info_violations,violations,line_coverage,new_line_coverage,lines,ncloc,ncloc_language_distribution,lines_to_cover,new_lines_to_cover,sqale_rating,new_maintainability_rating,major_violations,minor_violations,ncloc_data,new_blocker_violations,new_bugs,new_code_smells,new_critical_violations,new_info_violations,new_violations,new_lines,new_major_violations,new_minor_violations,new_security_hotspots,new_vulnerabilities,open_issues,quality_profiles,projects,public_api,public_documented_api_density,public_undocumented_api,quality_gate_details,alert_status,reliability_rating,new_reliability_rating,reliability_remediation_effort,new_reliability_remediation_effort,reopened_issues,security_hotspots,security_hotspots_reviewed,new_security_hotspots_reviewed,security_rating,new_security_rating,security_remediation_effort,new_security_remediation_effort,security_review_rating,new_security_review_rating,security_hotspots_reviewed_status,new_security_hotspots_reviewed_status,security_hotspots_to_review_status,new_security_hotspots_to_review_status,skipped_tests,statements,sqale_index,sqale_debt_ratio,new_sqale_debt_ratio,uncovered_conditions,new_uncovered_conditions,uncovered_lines,new_uncovered_lines,test_execution_time,test_errors,test_failures,tests,test_success_density,vulnerabilities,wont_fix_issues

### NEW

accepted_issues,new_technical_debt,analysis_from_sonarqube_9_4,high_impact_accepted_issues,blocker_violations,bugs,classes,code_smells,cognitive_complexity,comment_lines,comment_lines_density,comment_lines_data,branch_coverage,new_branch_coverage,conditions_to_cover,new_conditions_to_cover,confirmed_issues,sca_count_any_issue,new_sca_count_any_issue,coverage,new_coverage,critical_violations,complexity,last_commit_date,sca_rating_any_issue,sca_rating_licensing,new_sca_rating_licensing,sca_rating_vulnerability,new_sca_rating_vulnerability,new_sca_rating_any_issue,development_cost,new_development_cost,duplicated_blocks,new_duplicated_blocks,duplicated_files,duplicated_lines,duplicated_lines_density,new_duplicated_lines_density,new_duplicated_lines,duplications_data,effort_to_reach_maintainability_rating_a,executable_lines_data,false_positive_issues,files,functions,generated_lines,generated_ncloc,info_violations,violations,line_coverage,new_line_coverage,lines,ncloc,ncloc_language_distribution,lines_to_cover,new_lines_to_cover,maintainability_issues,sqale_rating,new_maintainability_rating,major_violations,minor_violations,ncloc_data,new_accepted_issues,new_blocker_violations,new_bugs,new_code_smells,new_critical_violations,new_info_violations,new_violations,new_lines,new_maintainability_issues,new_major_violations,new_minor_violations,new_reliability_issues,new_security_hotspots,new_security_issues,new_vulnerabilities,open_issues,quality_profiles,projects,public_api,public_documented_api_density,public_undocumented_api,pull_request_fixed_issues,quality_gate_details,alert_status,reliability_issues,reliability_rating,new_reliability_rating,reliability_remediation_effort,new_reliability_remediation_effort,reopened_issues,security_hotspots,security_hotspots_reviewed,new_security_hotspots_reviewed,security_issues,security_rating,new_security_rating,security_remediation_effort,new_security_remediation_effort

### Local Sonar comment_lines_density

```json
{
  "errors": [
    {
      "msg": "The following metric keys are not found: sca_count_any_issue, sca_rating_any_issue, new_sca_rating_vulnerability, sca_rating_vulnerability, new_sca_rating_licensing, new_sca_rating_any_issue, sca_rating_licensing, new_sca_count_any_issue"
    }
  ]
}
```

## Penalty Mapping

```go
penaltyMap := map[string]float64{
    "new_security_rating":          7.63,
    "new_reliability_rating":       6.87,
    "security_rating":              6.87,
    "critical_severity_vulns":      6.87, // Missing
    "new_security_hotspots":        6.23,
    "new_coverage":                 6.11,
    "reliability_rating":           6.11,
    "new_critical_violations":      6.11,
    "critical_violations":          5.35,
    "blocker_violations":           5.35,
    "high_severity_vulns":          5.35, // Missing
    "new_maintainability_rating":   4.59,
    "sqale_rating":                 4.59,
    "coverage":                     4.59,
    "new_duplicated_lines_density": 3.82,
    "medium_severity_vulns":        3.82, // Missing
    "new_major_violations":         3.82,
    "major_violations":             3.06,
    "duplicated_blocks":            3.06,
    "comment_lines_density":        2.29,
    "reopened_issues":              2.29,
    "low_severity_vulns":           1.53, // Missing
}
```

## SQL VIEW MEASUREMENTS

```sql
CREATE OR REPLACE VIEW vw_sonarqube_analyses AS
SELECT 
    sa.*,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "open_issues").value')#>>'{}', ''))::numeric as measure_open_issues,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "coverage").value')#>>'{}', ''))::numeric as measure_coverage,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_coverage").value')#>>'{}', ''))::numeric as measure_new_coverage,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "reliability_rating").value')#>>'{}', ''))::numeric as measure_reliability_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_reliability_rating").value')#>>'{}', ''))::numeric as measure_new_reliability_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "security_rating").value')#>>'{}', ''))::numeric as measure_security_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_security_rating").value')#>>'{}', ''))::numeric as measure_new_security_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "security_review_rating").value')#>>'{}', ''))::numeric as measure_security_review_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_security_review_rating").value')#>>'{}', ''))::numeric as measure_new_security_review_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "sqale_rating").value')#>>'{}', ''))::numeric as measure_sqale_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_maintainability_rating").value')#>>'{}', ''))::numeric as measure_new_sqale_rating,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "duplicated_lines").value')#>>'{}', ''))::numeric as measure_duplicated_lines,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "duplicated_lines_density").value')#>>'{}', ''))::numeric as measure_duplicated_lines_density,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "code_smells").value')#>>'{}', ''))::numeric as measure_code_smells,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "vulnerable_dependencies").value')#>>'{}', ''))::numeric as measure_vulnerable_dependencies,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "total_vulnerabilities").value')#>>'{}', ''))::numeric as measure_total_vulnerabilities,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "vulnerabilities").value')#>>'{}', ''))::numeric as measure_vulnerabilities,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "medium_severity_vulns").value')#>>'{}', ''))::numeric as measure_medium_severity_vulns,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "low_severity_vulns").value')#>>'{}', ''))::numeric as measure_low_severity_vulns,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "high_severity_vulns").value')#>>'{}', ''))::numeric as measure_high_severity_vulns,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "violations").value')#>>'{}', ''))::numeric as measure_violations,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "minor_violations").value')#>>'{}', ''))::numeric as measure_minor_violations,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "major_violations").value')#>>'{}', ''))::numeric as measure_major_violations,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "critical_violations").value')#>>'{}', ''))::numeric as measure_critical_violations,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "blocker_violations").value')#>>'{}', ''))::numeric as measure_blocker_violations,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "bugs").value')#>>'{}', ''))::numeric as measure_bugs,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "new_bugs").value')#>>'{}', ''))::numeric as measure_new_bugs,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "security_hotspots").value')#>>'{}', ''))::numeric as measure_security_hotspots,
    (NULLIF(jsonb_path_query_first(sa.measures, '$.data.component.measures[*] ? (@.metric == "lines").value')#>>'{}', ''))::numeric as measure_lines
FROM sonarqube_analyses sa;
```

## Missing Metrics

```json
security_review_rating
new_security_review_rating
vulnerable_dependencies
total_vulnerabilities
vulnerabilities
medium_severity_vulns
low_severity_vulns
high_severity_vulns
```

## Possible workarounds

### New keys in response

- https://docs.sonarsource.com/sonarqube-server/user-guide/code-metrics/metrics-definition

```json
      {
        "metric": "security_issues",
        "value": "{\"total\":195,\"HIGH\":25,\"MEDIUM\":69,\"LOW\":101,\"INFO\":0,\"BLOCKER\":0}"
      },
      {
        "metric": "maintainability_issues",
        "value": "{\"total\":45,\"HIGH\":13,\"MEDIUM\":10,\"LOW\":21,\"INFO\":1,\"BLOCKER\":0}"
      },
      {
        "metric": "new_vulnerabilities",
        "periods": [{ "index": 1, "value": "0", "bestValue": true }]
      },
      {
        "metric": "reliability_issues",
        "value": "{\"total\":1,\"HIGH\":0,\"MEDIUM\":0,\"LOW\":1,\"INFO\":0,\"BLOCKER\":0}"
      },
      {
        "metric": "new_sca_rating_vulnerability", // only sonar cloud + advance security
        "periods": [{ "index": 1, "value": "4.0", "bestValue": false }]
      },
      {
        "metric": "sca_rating_vulnerability", // only sonar cloud + advance security
        "value": "4.0",
        "bestValue": false
      },
      {
        "metric": "new_sca_count_any_issue", // only sonar cloud + advance security // The total number of dependency risks raised for the first time on new code.


        "periods": [{ "index": 1, "value": "127", "bestValue": false }] // only sonar cloud + advance security
      },
      { "metric": "sca_rating_any_issue", "value": "4.0", "bestValue": false }, // only sonar cloud + advance security // Rating related to dependency risks
      { "metric": "sca_count_any_issue", "value": "127", "bestValue": false }, // only sonar cloud + advance security // The total number of dependency risks.
      {
        "metric": "new_sca_rating_licensing",// only sonar cloud + advance security // Rating related to dependency licenses in new code.


        "periods": [{ "index": 1, "value": "1.0", "bestValue": true }]
      },
      {
        "metric": "new_sca_rating_any_issue", // only sonar cloud + advance security // Rating related to dependency risks in new code.


        "periods": [{ "index": 1, "value": "4.0", "bestValue": false }]
      },
      { "metric": "sca_rating_licensing", "value": "1.0", "bestValue": true }, // only sonar cloud + advance security // Rating related to dependency licenses. License risks always have a rating of D. so using a threshold of E will not fail the quality gate.

```

## Dependency Check + Sonar Cloud

SonarCloud doesn't currently support importing OWASP Dependency Check reports. 

- export to SARIF format and import it using `sonar.sarifReportPaths`
  - https://jeremylong.github.io/DependencyCheck/dependency-check-cli/arguments.html
- Third-Party Tools:
  - https://www.bitegarden.com/es/sonarcloud-dependency-check


## Changes in Sonar properties for Sonar Cloud

- https://docs.sonarsource.com/sonarqube-server/analyzing-source-code/analysis-parameters

```properties
sonar.organization=org-name
```

## Sonar cloud supported languages

- https://docs.sonarsource.com/sonarqube-cloud/advanced-setup/languages/overview

## Jira Integration

- https://docs.sonarsource.com/sonarqube-cloud/managing-your-projects/administering-your-projects/jira-integration/

## Universal Lintern

- https://megalinter.io/latest/
-https://github.com/caramelomartins/awesome-linters