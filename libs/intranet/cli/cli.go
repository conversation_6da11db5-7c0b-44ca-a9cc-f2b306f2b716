package cli

import (
	"context"
	"errors"
	"fmt"
	"log"
	"log/slog"
	"maps"
	"slices"
	"sort"
	"strings"

	corerepo "sa-intranet/core/repository"
	"sa-intranet/db/migrate/migrations"
	"sa-intranet/policies"
	"sa-intranet/usecase/auth/model"
	"sa-intranet/usecase/auth/repository"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/spf13/cobra"
	"github.com/uptrace/bun"
	"github.com/uptrace/bun/migrate"
)

// Define static errors
var (
	ErrRoleNotExist   = errors.New("role does not exist in list of roles")
	ErrUserNotFound   = errors.New("user not found")
	ErrUsernameExists = errors.New("user with username already exists")
	ErrEmailExists    = errors.New("user with email already exists")
)

func createCommands(migrator *migrate.Migrator) []*cobra.Command {
	return []*cobra.Command{
		createInitCommand(migrator),
		createMigrateCommand(migrator),
		createRollbackCommand(migrator),
		createLockCommand(migrator),
		createGoMigrationCommand(migrator),
		createSQLMigrationCommand(migrator),
		createTxSQLMigrationCommand(migrator),
		createStatusCommand(migrator),
		createMarkAppliedCommand(migrator),
	}
}

func createInitCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "init",
		Short: "initialize database",
		Long:  `initialize database`,
		RunE: func(c *cobra.Command, args []string) error {
			return migrator.Init(c.Context())
		},
	}
}

// withMigratorLock executes the given function with migrator lock/unlock handling
func withMigratorLock(
	ctx context.Context,
	migrator *migrate.Migrator,
	fn func(context.Context) (*migrate.MigrationGroup, error),
) (*migrate.MigrationGroup, error) {
	if err := migrator.Lock(ctx); err != nil {
		return nil, err
	}

	defer func() {
		if err := migrator.Unlock(ctx); err != nil {
			slog.Error("failed to unlock migrations", "error", err)
		}
	}()

	return fn(ctx)
}

func createMigrateCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "migrate",
		Short: "migrate database",
		Long:  `migrate database`,
		RunE: func(c *cobra.Command, args []string) error {
			group, err := withMigratorLock(c.Context(), migrator, func(ctx context.Context) (*migrate.MigrationGroup, error) {
				group, err := migrator.Migrate(ctx)
				if group.IsZero() {
					log.Printf("there are no new migrations to run (database is up to date)\n")
					return nil, err
				}
				if err != nil {
					return nil, err
				}
				return group, nil
			})
			if err != nil {
				return err
			}

			log.Printf("migrated to %s\n", group)
			return nil
		},
	}
}

func createRollbackCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "rollback",
		Short: "rollback the last migration group",
		Long:  `rollback the last migration group`,
		RunE: func(c *cobra.Command, args []string) error {
			group, err := withMigratorLock(c.Context(), migrator, func(ctx context.Context) (*migrate.MigrationGroup, error) {
				group, err := migrator.Rollback(ctx)

				if group.IsZero() {
					log.Printf("there are no groups to roll back\n")
					return group, err
				}
				return group, nil
			})
			if err != nil {
				return err
			}

			log.Printf("rolled back %s\n", group)
			return nil
		},
	}
}

func createLockCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "lock",
		Short: "lock migrations",
		Long:  `lock migrations`,
		RunE: func(c *cobra.Command, args []string) error {
			return migrator.Lock(c.Context())
		},
	}
}

func createGoMigrationCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "create-go",
		Short: "create a new migration file",
		Long:  `create a new migration file`,
		RunE: func(c *cobra.Command, args []string) error {
			name := strings.Join(args, "_")
			mf, err := migrator.CreateGoMigration(c.Context(), name)
			if err != nil {
				return err
			}
			log.Printf("created migration %s (%s)\n", mf.Name, mf.Path)
			return nil
		},
	}
}

func createSQLMigrationCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "create_sql [name]",
		Short: "create up and down SQL migrations",
		RunE: func(c *cobra.Command, args []string) error {
			name := strings.Join(args, "_")
			files, err := migrator.CreateSQLMigrations(c.Context(), name)
			if err != nil {
				return err
			}
			for _, mf := range files {
				log.Printf("created migration %s (%s)\n", mf.Name, mf.Path)
			}
			return nil
		},
	}
}

func createTxSQLMigrationCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "create_tx_sql [name]",
		Short: "create up and down transactional SQL migrations",
		RunE: func(c *cobra.Command, args []string) error {
			name := strings.Join(args, "_")
			files, err := migrator.CreateTxSQLMigrations(c.Context(), name)
			if err != nil {
				return err
			}
			for _, mf := range files {
				log.Printf("created transaction migration %s (%s)\n", mf.Name, mf.Path)
			}
			return nil
		},
	}
}

func createStatusCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "status",
		Short: "print migrations status",
		RunE: func(c *cobra.Command, args []string) error {
			ms, err := migrator.MigrationsWithStatus(c.Context())
			if err != nil {
				return err
			}
			log.Printf("migrations: %s\n", ms)
			log.Printf("unapplied migrations: %s\n", ms.Unapplied())
			log.Printf("last migration group: %s\n", ms.LastGroup())
			return nil
		},
	}
}

func createMarkAppliedCommand(migrator *migrate.Migrator) *cobra.Command {
	return &cobra.Command{
		Use:   "mark_applied",
		Short: "mark migrations as applied without actually running them",
		RunE: func(c *cobra.Command, args []string) error {
			group, err := migrator.Migrate(c.Context(), migrate.WithNopMigration())
			if err != nil {
				return err
			}
			if group.IsZero() {
				log.Printf("there are no new migrations to mark as applied\n")
				return nil
			}
			log.Printf("marked as applied %s\n", group)
			return nil
		},
	}
}

func NewDBCommand(i *do.Injector) *cobra.Command {
	db := do.MustInvoke[*bun.DB](i)
	migrator := migrate.NewMigrator(db, migrations.Migrations)

	dbCmd := &cobra.Command{
		Use:   "db",
		Short: "database migrations",
		Long:  `database migrations`,
	}

	commands := createCommands(migrator)
	for _, cmd := range commands {
		dbCmd.AddCommand(cmd)
	}

	return dbCmd
}

func NewOperatorCommand(i *do.Injector) *cobra.Command {
	operatorCmd := &cobra.Command{
		Use:   "operator",
		Short: "commands for operators",
		Long:  `operator commands, are used to manage the application from the command line`,
	}

	userRolesCmd := createUserRolesCommand(i)
	operatorCmd.AddCommand(userRolesCmd)

	usersCreateCmd := createUsersCreateCommand(i)
	operatorCmd.AddCommand(usersCreateCmd)

	return operatorCmd
}

func createUserRolesCommand(i *do.Injector) *cobra.Command {
	return &cobra.Command{
		Use:   "user:roles [email] [role]",
		Short: "update user role",
		Long:  `update a user's role by email`,
		Args:  cobra.ExactArgs(2),
		RunE: func(cmd *cobra.Command, args []string) error {
			return executeUserRoleUpdate(i, cmd, args)
		},
	}
}

func createUsersCreateCommand(i *do.Injector) *cobra.Command {
	return &cobra.Command{
		Use:   "user:create [username] [email] [role]",
		Short: "create a new user",
		Long:  `create a new user with username, email, and role`,
		Args:  cobra.ExactArgs(3),
		RunE: func(cmd *cobra.Command, args []string) error {
			return executeUserCreate(i, cmd, args)
		},
	}
}

func executeUserRoleUpdate(i *do.Injector, cmd *cobra.Command, args []string) error {
	email := strings.TrimSpace(args[0])
	role := strings.TrimSpace(args[1])

	userRepo := do.MustInvoke[repository.UserRepository](i)
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	if err := validateRole(role, rbac); err != nil {
		return err
	}

	user, err := findUserByEmail(cmd.Context(), email, userRepo)
	if err != nil {
		return err
	}

	if err := updateUserRole(cmd.Context(), user.ID, role, userRepo); err != nil {
		return err
	}

	log.Printf("User %s role updated to %s\n", email, role)

	return nil
}

func executeUserCreate(i *do.Injector, cmd *cobra.Command, args []string) error {
	username := strings.TrimSpace(args[0])
	email := strings.TrimSpace(args[1])
	role := strings.TrimSpace(args[2])

	userRepo := do.MustInvoke[repository.UserRepository](i)
	rbac := do.MustInvoke[*policies.RBACPolicy](i)

	if err := validateRole(role, rbac); err != nil {
		return err
	}

	if err := createUser(cmd.Context(), username, email, role, userRepo); err != nil {
		return err
	}

	log.Printf("User created successfully: %s (%s) with role %s\n", username, email, role)

	return nil
}

func validateRole(role string, rbac *policies.RBACPolicy) error {
	if _, exists := rbac.Roles[role]; !exists {
		roles := slices.Collect(maps.Keys(rbac.Roles))
		sort.Strings(roles)

		return fmt.Errorf("%w: '%s' not in %v", ErrRoleNotExist, role, roles)
	}

	return nil
}

func findUserByEmail(ctx context.Context, email string, userRepo repository.UserRepository) (*model.User, error) {
	fields := []string{"id"}
	params := corerepo.PaginationParams[repository.UserFilter]{
		Page:     1,
		PageSize: 1,
		Filters: repository.UserFilter{
			Email: email,
		},
		DisableCount: true,
		Fields:       fields,
		AllowFields:  fields,
	}

	result, err := userRepo.List(ctx, params)
	if err != nil {
		return nil, fmt.Errorf("failed to find user: %w", err)
	}

	user, ok := result.First()
	if !ok {
		return nil, fmt.Errorf("%w: %s", ErrUserNotFound, email)
	}

	return &user, nil
}

func updateUserRole(ctx context.Context, userID uuid.UUID, role string, userRepo repository.UserRepository) error {
	u := model.User{
		ID:   userID,
		Role: role,
	}

	updateFields := []string{"role"}

	_, err := userRepo.Save(
		ctx,
		&u,
		corerepo.WithSaveNew(false),
		corerepo.WithSaveUpdateFields(updateFields),
		corerepo.WithSaveAllowUpdateFields(updateFields),
	)
	if err != nil {
		return fmt.Errorf("failed to update user role: %w", err)
	}

	return nil
}

func createUser(ctx context.Context, username, email, role string, userRepo repository.UserRepository) error {
	// Check if user already exists
	if err := checkUserExists(ctx, username, email, userRepo); err != nil {
		return err
	}

	u := model.User{
		Username: username,
		Email:    email,
		Role:     role,
	}

	_, err := userRepo.Save(
		ctx,
		&u,
		corerepo.WithSaveNew(true),
	)
	if err != nil {
		return fmt.Errorf("failed to create user: %w", err)
	}

	return nil
}

func checkUserExists(ctx context.Context, username, email string, userRepo repository.UserRepository) error {
	// Check if username already exists
	params := corerepo.PaginationParams[repository.UserFilter]{
		Page:     1,
		PageSize: 1,
		Filters: repository.UserFilter{
			Username: username,
		},
		DisableCount: true,
		Fields:       []string{"id"},
		AllowFields:  []string{"id"},
	}

	result, err := userRepo.List(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to check username existence: %w", err)
	}

	if result.TotalItems > 0 {
		return fmt.Errorf("%w: '%s'", ErrUsernameExists, username)
	}

	// Check if email already exists
	params.Filters = repository.UserFilter{
		Email: email,
	}

	result, err = userRepo.List(ctx, params)
	if err != nil {
		return fmt.Errorf("failed to check email existence: %w", err)
	}

	if result.TotalItems > 0 {
		return fmt.Errorf("%w: '%s'", ErrEmailExists, email)
	}

	return nil
}
