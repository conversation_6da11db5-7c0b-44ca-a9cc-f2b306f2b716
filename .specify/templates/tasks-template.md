# Tasks: [FEATURE NAME]

**Input**: Design documents from `/specs/[###-feature-name]/`
**Prerequisites**: plan.md (required), research.md, data-model.md, contracts/

## Execution Flow (main)
```
1. Load plan.md from feature directory
   → If not found: ERROR "No implementation plan found"
   → Extract: tech stack, libraries, structure
2. Load optional design documents:
   → data-model.md: Extract entities → model tasks
   → contracts/: Each file → contract test task
   → research.md: Extract decisions → setup tasks
3. Generate tasks by category:
   → Setup: project init, dependencies, linting
   → Tests: contract tests, integration tests
   → Core: models, services, CLI commands
   → Integration: DB, middleware, logging
   → Polish: unit tests, performance, docs
4. Apply task rules:
   → Different files = mark [P] for parallel
   → Same file = sequential (no [P])
   → Tests before implementation (TDD)
5. Number tasks sequentially (T001, T002...)
6. Generate dependency graph
7. Create parallel execution examples
8. Validate task completeness:
   → All contracts have tests?
   → All entities have models?
   → All endpoints implemented?
9. Return: SUCCESS (tasks ready for execution)
```

## Format: `[ID] [P?] Description`
- **[P]**: Can run in parallel (different files, no dependencies)
- Include exact file paths in descriptions

## Path Conventions
- **Single project**: `src/`, `tests/` at repository root
- **Web app**: `backend/src/`, `frontend/src/`
- **Mobile**: `api/src/`, `ios/src/` or `android/src/`
- Paths shown below assume single project - adjust based on plan.md structure

## Phase 3.1: Setup
- [ ] T001 Create project structure per implementation plan
- [ ] T002 Initialize [language] project with [framework] dependencies
- [ ] T003 [P] Configure linting and formatting tools

## Phase 3.2: Tests First (TDD) ⚠️ MUST COMPLETE BEFORE 3.3
**CRITICAL: These tests MUST be written and MUST FAIL before ANY implementation**
**CONSTITUTION COMPLIANCE: Testing Standards principle requires TDD approach**
- [ ] T004 [P] Contract test POST /api/users in tests/contract/test_users_post.go
- [ ] T005 [P] Contract test GET /api/users/{id} in tests/contract/test_users_get.go
- [ ] T006 [P] Integration test user registration in tests/integration/test_registration.go (use testcontainers-go)
- [ ] T007 [P] Integration test auth flow in tests/integration/test_auth.go
- [ ] T008 [P] Unit test coverage target setup (80% minimum per Code Quality First principle)

## Phase 3.3: Core Implementation (ONLY after tests are failing)
**CONSTITUTION COMPLIANCE: All code must pass quality gates before merge**
- [ ] T009 [P] User model in libs/intranet/usecase/auth/model/user.go
- [ ] T010 [P] UserService CRUD in libs/intranet/usecase/auth/service/user_service.go
- [ ] T011 [P] User interactor in libs/intranet/usecase/auth/service/in/user_interactor.go
- [ ] T012 [P] User presenter in libs/intranet/usecase/auth/service/out/user_presenter.go
- [ ] T013 User repository in libs/intranet/usecase/auth/repository/user_repository.go
- [ ] T014 POST /api/users endpoint in services/backend/http/
- [ ] T015 GET /api/users/{id} endpoint in services/backend/http/
- [ ] T016 Input validation using validator library
- [ ] T017 Error handling with proper HTTP status codes and logging

## Phase 3.4: Quality Gates & Integration
**CONSTITUTION COMPLIANCE: Security First and Performance Standards principles**
- [ ] T018 Configure golangci-lint compliance check
- [ ] T019 Run security scans (govulncheck, trivy)
- [ ] T020 Performance test API endpoints (<200ms p95 requirement)
- [ ] T021 Connect UserService to database with proper connection pooling
- [ ] T022 JWT auth middleware implementation
- [ ] T023 Structured logging with context.Context
- [ ] T024 Security headers and CORS configuration
- [ ] T025 SonarQube quality gate validation

## Phase 3.5: User Experience & Documentation
**CONSTITUTION COMPLIANCE: User Experience Consistency principle**
- [ ] T026 [P] Frontend React components (if applicable) with TailwindCSS
- [ ] T027 [P] Mobile-responsive design validation
- [ ] T028 [P] Accessibility compliance check (WCAG 2.1 AA)
- [ ] T029 [P] API documentation update (GoDoc comments)
- [ ] T030 [P] Bundle size validation (<500KB requirement)
- [ ] T031 Coverage report generation and validation (80% threshold)
- [ ] T032 Manual testing checklist execution
- [ ] T033 Performance monitoring setup

## Dependencies
- Tests (T004-T007) before implementation (T008-T014)
- T008 blocks T009, T015
- T016 blocks T018
- Implementation before polish (T019-T023)

## Parallel Example
```
# Launch T004-T007 together:
Task: "Contract test POST /api/users in tests/contract/test_users_post.py"
Task: "Contract test GET /api/users/{id} in tests/contract/test_users_get.py"
Task: "Integration test registration in tests/integration/test_registration.py"
Task: "Integration test auth in tests/integration/test_auth.py"
```

## Notes
- [P] tasks = different files, no dependencies
- Verify tests fail before implementing
- Commit after each task
- Avoid: vague tasks, same file conflicts

## Task Generation Rules
*Applied during main() execution*

1. **From Contracts**:
   - Each contract file → contract test task [P]
   - Each endpoint → implementation task
   
2. **From Data Model**:
   - Each entity → model creation task [P]
   - Relationships → service layer tasks
   
3. **From User Stories**:
   - Each story → integration test [P]
   - Quickstart scenarios → validation tasks

4. **Ordering**:
   - Setup → Tests → Models → Services → Endpoints → Polish
   - Dependencies block parallel execution

## Validation Checklist
*GATE: Checked by main() before returning*

- [ ] All contracts have corresponding tests
- [ ] All entities have model tasks
- [ ] All tests come before implementation
- [ ] Parallel tasks truly independent
- [ ] Each task specifies exact file path
- [ ] No task modifies same file as another [P] task