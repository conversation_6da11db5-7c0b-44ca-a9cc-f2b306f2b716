<!--
Sync Impact Report:
- Version change: Initial → 1.0.0
- Added principles: Code Quality First, Testing Standards, User Experience Consistency, Performance Standards, Security First
- Added sections: Quality Gates, Development Workflow
- Templates requiring updates: ✅ Plan template validated, ✅ Other templates compatible
- Follow-up TODOs: None - all placeholders filled
-->

# SA Intranet Constitution

## Core Principles

### I. Code Quality First (NON-NEGOTIABLE)
All new code MUST achieve minimum 80% test coverage before merge. Code quality gates are enforced through automated CI/CD pipeline with SonarQube analysis, golangci-lint compliance, and security vulnerability scans (govulncheck, Trivy). No exceptions - quality debt is technical debt that compounds over time.

Rationale: The 80% coverage threshold ensures thorough testing while maintaining development velocity. Quality gates prevent accumulation of technical debt and maintain long-term codebase health.

### II. Testing Standards (NON-NEGOTIABLE)
Test-Driven Development (TDD) is mandatory for all new features. Tests MUST be written first, validated to fail, then implementation follows. All code requires: unit tests (>80% coverage), integration tests for cross-service communication, and end-to-end tests for critical user journeys. Use testcontainers-go for service integration tests.

Rationale: TDD ensures design clarity, prevents regression bugs, and creates living documentation. The testing pyramid approach balances thoroughness with execution speed.

### III. User Experience Consistency
Frontend and backend MUST maintain consistent data contracts, error handling patterns, and response formats. Use DTOs for all API boundaries. All user-facing features require responsive design (mobile-first), accessibility compliance (WCAG 2.1 AA), and consistent component library usage (TailwindCSS + React functional components).

Rationale: Consistency reduces cognitive load for users and developers, improves maintainability, and ensures professional polish across all touchpoints.

### IV. Performance Standards
API endpoints MUST respond within 200ms (p95) for standard operations, 1000ms for complex queries. Database queries MUST be optimized and monitored. Frontend bundles MUST be <500KB gzipped. Memory usage MUST not exceed baseline +20% during normal operations. Performance budgets are enforced in CI/CD.

Rationale: Performance directly impacts user satisfaction and system scalability. Clear thresholds enable proactive optimization and prevent performance regression.

### V. Security First
All code MUST pass security scans (Trivy, govulncheck) with zero high/critical vulnerabilities. Authentication/authorization MUST use proven patterns (JWT, RBAC). Sensitive data MUST be encrypted at rest and in transit. Security headers MUST be implemented on all HTTP responses. Regular dependency updates MUST be automated.

Rationale: Security vulnerabilities can cause irreparable damage to users and business. Proactive security practices prevent costly breaches and maintain trust.

## Quality Gates

All pull requests MUST pass these automated gates before merge:
- 80%+ test coverage (verified by SonarQube)
- golangci-lint passes with zero issues
- Security scans pass (Trivy, govulncheck) 
- Performance budgets met (API response times, bundle sizes)
- SonarQube quality gate passes (code smells, duplications, maintainability)
- All tests pass (unit, integration, e2e)

Manual gates:
- Code review by senior developer
- Design review for UI/UX changes
- Architecture review for cross-service changes

## Development Workflow

**Branch Strategy**: Trunk-based development with short-lived feature branches
**Commit Format**: Conventional Commits (max 100 characters)
**Code Style**: Enforced by golangci-lint configuration, consistent across Go and TypeScript
**Documentation**: All public APIs require GoDoc comments, all features require README updates
**Deployment**: Blue-green deployment with automated rollback on health check failures

Quality assurance process:
1. Feature specification in `/specs/` directory
2. TDD implementation with tests first
3. Code review with constitution compliance check
4. Automated quality gates validation
5. Staging deployment and validation
6. Production deployment with monitoring

## Governance

This constitution supersedes all other development practices and guidelines. All team members MUST verify compliance during code reviews. Any violation requires explicit justification and remediation plan.

Amendment process:
- Propose changes via RFC (Request for Comments) document
- Team discussion and approval (majority vote)
- Update dependent templates and documentation
- Version increment following semantic versioning
- Migration plan for existing code if needed

Compliance reviews are conducted quarterly to ensure adherence and identify improvement opportunities.

**Version**: 1.0.0 | **Ratified**: 2025-10-06 | **Last Amended**: 2025-10-06