---
title: "Complexity Level Catalog for JIRA"
categories:
  - Position
  - Architecture
  - Role
menu:
  main:
    name: Complexity Level Catalog for JIRA
    parent: Architecture Handbook
---

# 🗂 Complexity Level Catalog for JIRA

### Field Type

- **Custom Field Name**: `Complexity Level`
    
- **Field Type**: _Single Select List_ (Dropdown)
    
- **Applies To**: Epics, Stories, Tasks, Bugs (you choose scope)
    

---

### 🎯 Purpose

To provide a consistent way of estimating **complexity** beyond just “Story Points” or “Effort.” This helps teams:

- Align expectations across projects.
    
- Improve planning accuracy.
    
- Differentiate between time effort vs. inherent complexity (dependencies, risks, uncertainty).
    

---

### 📊 Suggested Complexity Levels

#### 1. **Trivial**

- Straightforward, almost no risk.
    
- No dependencies, no special knowledge needed.
    
- Example: text change, configuration update, typo fix.
    

#### 2. **Low**

- Small, well understood task.
    
- Few dependencies or edge cases.
    
- Example: small UI adjustment, adding a field in a form.
    

#### 3. **Medium**

- Requires design consideration, some dependencies.
    
- Known solution, but needs coordination.
    
- Example: integrate with existing service, modify DB schema with migrations.
    

#### 4. **High**

- Multiple dependencies or external coordination.
    
- Requires investigation, risk of rework.
    
- Example: new API integration, refactoring a core module, cross-team impact.
    

#### 5. **Very High**

- High uncertainty, requires discovery.
    
- Large architectural or business impact.
    
- Example: migrating a service, building new module, handling complex compliance rules.
    

#### 6. **Extreme**

- High technical + business risk.
    
- Requires executive/stakeholder alignment.
    
- Might span multiple sprints/teams.
    
- Example: re-platforming, data center migration, fundamental redesign.



| Level         | Scope | Dependencies | Risk/Uncertainty | Example         |
| ------------- | ----- | ------------ | ---------------- | --------------- |
| **Trivial**   | <1h   | None         | None             | Text change     |
| **Low**       | <1d   | Minimal      | Low              | UI tweak        |
| **Medium**    | 1–3d  | Few          | Manageable       | New endpoint    |
| **High**      | 3–5d  | Several      | Moderate         | API integration |
| **Very High** | 1–2w  | Many         | High             | New module      |
| **Extreme**   | >2w   | Cross-teams  | Very High        | Migration       |
