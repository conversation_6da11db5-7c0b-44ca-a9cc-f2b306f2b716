---
title: "Architects Metrics"
categories:
  - Position
  - Architecture
  - Role
menu:
  main:
    name: Architects Metrics
    parent: Architecture Handbook
---

## Official Metrics


| Role                | KPI Name                               | Description | Target/ Threshold | Role responsible for KPI evaluation | Weightage | KPI applies to projects <= 8 weeks | Applies to public | Role Responsible for KPI in Public | Service Model | Code | Measurement Frequency | Review Frequency |
|---------------------|----------------------------------------|-------------|-------------------|--------------------------------------|-----------|-----------------------------------|-------------------|-----------------------------------|---------------|------|-----------------------|------------------|
| Business Architect  | Change Failure Rate                    | Source: LinearB. <br><br>Notes: Uses LinearB’s Change Failure Rate metric, which measures the % of deployments that result in a failure requiring a hotfix, rollback, or patch. <br><br>How to Track: Monitor CFR in LinearB dashboards; filter by production deployments only. <br><br>Why It Matters: Indicates the stability and quality of delivered changes. A low CFR reflects strong architectural alignment with business goals and fewer post-release issues. | < 15% (ideally < 5%) | Agile Facilitator | 25% | Applies For all project lengths | Yes | Senior Project Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Business Architect  | JIRA Ticket Scope Drift                | Source: JIRA. <br><br>Notes: Each Epic and User Story must have an "Original Estimate" (hrs) set by the Business Architect. Compare it to the final developer estimate. <br><br>How to Track: Use JIRA dashboards or filters to count open tech-debt-arch-erosion tickets and compare against the total number of active Epics. <br><br>Why It Matters: Tracks architectural integrity over time. Ensures technical debt related to erosion is visible, measurable, and prioritized before it undermines scalability, maintainability, or performance. | <= 40% variance | Agile Facilitator | 20% | Applies For all project lengths | Yes | Senior Project Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Application Architect | Code Quality Score                   | Source: JIRA. <br><br>Notes: Tracks long-term maintainability and adherence to quality standards. <br><br>How to Track: Check the Code quality score in the CQM dashboard. <br><br>Why It Matters: Prevents architecture erosion and reduces future cost of change or bugs. | SonarQube score ≥ 80% | Agile Facilitator | 40% | Applies For all project lengths | Yes | Senior Project Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Application Architect | Refactor Time Ratio                  | Source: LinearB. <br><br>Notes: Measures refactoring of code more than 21 days old. Large refactoring in a single release risks destabilizing existing functionality. <br><br>How to Track: Use LinearB “Refactor” metric focused on changes to code > 21 days old, expressed as % of total dev time. <br><br>Why It Matters: High refactor rates on older code indicate potential architectural misalignment or technical debt, increasing risk of regressions. | < 15% of total dev time spent on refactoring code older than 21 days | Agile Facilitator | 30% | Applies For all project lengths | Yes | Senior Project Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Application Architect | Cycle Time Metric - Review Time      | Uses LinearB’s “Review Time” metric, which captures the full time a pull request spends in review before merging. <br><br>Why It Matters: Long review times indicate decision-making bottlenecks or overloaded reviewers. Fast, high-quality reviews help maintain architectural velocity and team morale. | < 16 hours | Agile Facilitator | 30% | Applies For all project lengths | Yes | Senior Project Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Business Architect  | Documented Technical Risk Ratio (DTRR) | Measures the architect’s accountability in identifying and escalating risks, regardless of delivery decisions. Percentage of technical decisions with impact that were formally documented and communicated as risks prior to release. Source: Jira+Documentation | >=90% | Agile Facilitator | 30% | Applies For all project lengths | Yes | Public Sector Account Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Business Architect  | Architectural Refactor Completion Rate (ARCR) | Measures architectural commitments are followed through and not accumulated as long-term technical debt. Percentage of refactors derived from architectural decisions that were completed in the current or following release. Source: Jira | <= 10% | Agile Facilitator | 25% | Applies For all project lengths | Yes | Public Sector Account Manager | Digital Solutions | DS&PB- | Bi-Monthly | Monthly |
| Business Architect  | CSAT                                   | Customer Satisfaction Score as reported by the client, typically during MBR sessions or periodic check-ins. It reflects the client’s perception of the individual's performance, communication, and collaboration. | <90%: Below expectations <br>=90%: On target <br>>90%: Exceeds expectations | Service Delivery Leader | 70% | Yes |  |  | Staff Augmentation | SA- | Monthly | Monthly |
| Application Architect | CSAT                                | Customer Satisfaction Score as reported by the client, typically during MBR sessions or periodic check-ins. It reflects the client’s perception of the individual's performance, communication, and collaboration. | <90%: Below expectations <br>=90%: On target <br>>90%: Exceeds expectations | Service Delivery Leader | 70% | Yes |  |  | Staff Augmentation | SA- | Monthly | Monthly |
| Business Architect  | Trust Score                           | Measures individual accountability and operational performance. The score starts at 100% each month and may be reduced based on the following conditions: <br>- ⚪ No alerts: Full score retained (100%) <br>- 🟠 Internal warning (SDL observation of risk behavior): –10% <br>- 🔴 Formal escalation from client (raised directly or via CSM): –30% <br><br>Warnings and escalations should be documented by the CSM & SDL in the BaseCamp Logs. |  | Service Delivery Leader | 30% |  |  |  | Staff Augmentation | SA- | Monthly | Monthly |
| Application Architect | Trust Score                        | Measures individual accountability and operational performance. The score starts at 100% each month and may be reduced based on the following conditions: <br>- ⚪ No alerts: Full score retained (100%) <br>- 🟠 Internal warning (SDL observation of risk behavior): –10% <br>- 🔴 Formal escalation from client (raised directly or via CSM): –30% <br><br>Warnings and escalations should be documented by the CSM & SDL in the BaseCamp Logs. |  | Service Delivery Leader | 30% |  |  |  | Staff Augmentation | SA- | Monthly | Monthly |

