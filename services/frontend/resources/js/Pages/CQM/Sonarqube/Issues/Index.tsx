import { usePage, router } from '@inertiajs/react';
import React, { useState, useEffect } from 'react';
import Paginator from '@/components/Paginator';
import Breadcrumbs from '@/components/Breadcrumbs';

interface TextRange {
  startLine: number;
  endLine: number;
  startOffset: number;
  endOffset: number;
}

interface Location {
  component?: string;
  textRange: TextRange;
  message?: string;
}

interface Flow {
  locations?: Location[];
}

interface MessageFormatting {
  start: number;
  end: number;
  type: string; // Could be "CODE", "STRONG", etc.
}

interface Issue {
  key: string;
  rule: string;
  severity: string;
  component: string;
  project: string;
  line?: number;
  hash?: string;
  status: string;
  message: string;
  type: string;
  creationDate: string;
  updateDate: string;
  tags: string[];
  author?: string;
  effort?: string;
  debt?: string;
  resolution?: string;
  closeDate?: string;
  textRange?: TextRange;
  flows: Flow[];
  scope?: string;
  quickFixAvailable?: boolean;
  messageFormattings: MessageFormatting[];
  url?: string;
  sonarURL?: string;
  jiraIssueKey?: string;
}


interface Pagination {
  totalCount : number;
  currentPage : number;
  perPage: number;
}

interface JiraProject {
  id: string;
  projectKey: string;
  name: string;
  
}


// Define the page props interface
interface PageProps {
  projectID: string;
  issues: Issue[];
  errors: string[];
  updatedIssues: Issue[];
  pagination: Pagination;
  jiraProject: JiraProject;
  [key: string]: any; // Add an index signature to satisfy the constraint
}

export default function Issues() {
  // Helper function to copy text to clipboard using modern API
  const copyToClipboard = async (text: string) => {
    try {
      if (navigator.clipboard && globalThis.isSecureContext) {
        await navigator.clipboard.writeText(text);
      } else {
        // Fallback using textarea selection for older browsers
        const textArea = document.createElement('textarea');
        textArea.value = text;
        document.body.appendChild(textArea);
        textArea.select();
        textArea.setSelectionRange(0, 99999); // For mobile devices
        textArea.remove();
      }
      alert('Prompt copied to clipboard!');
    } catch (err) {
      console.error('Failed to copy text: ', err);
      alert('Failed to copy prompt to clipboard');
    }
  };

  const [currentPage, setCurrentPage] = useState(1);
  // Use the Page type from Inertia
  const page = usePage<PageProps>();
  const { projectID, errors, issues, updatedIssues, pagination, jiraProject } = page.props;

  // Initialize local state from props
  const [sonarIssues, setSonarIssues] = useState(issues);
  const [errorsState, setErrorsState] = useState(errors);
  // Add loading state to track which issues are being processed
  const [loadingIssues, setLoadingIssues] = useState<Record<string, boolean>>({});

  // Selection state management
  const [selectedIssues, setSelectedIssues] = useState<Set<string>>(new Set());

  // Computed value for select-all checkbox state
  const selectAllChecked = sonarIssues.length > 0 && sonarIssues.every(issue => selectedIssues.has(issue.key));

  useEffect(() => {
    const params = new URLSearchParams(globalThis.location.search);
    const pageParam = params.get('page');
    if (pageParam) {
      setCurrentPage(Number(pageParam));
    }
  }, []);


  // Detect changes in errors prop
  useEffect(() => {
    setSonarIssues(issues);
  }, [issues]);


  // Detect changes in errors prop
  useEffect(() => {
    setErrorsState(errors);
  }, [errors]);

  // Detect changes in errors prop
  useEffect(() => {
    setSonarIssues(issues);
  }, [issues]);

  useEffect(() => {
    const issues = sonarIssues.map((issue) => {
      const updatedIssue = Array.from(updatedIssues).find((updated) => updated.key === issue.key);
      if (updatedIssue) {
        Object.assign(issue, updatedIssue);
      }
      return issue;
    })
    setSonarIssues(issues);
  }, [updatedIssues]);

  // Selection handler functions
  const handleIssueSelect = (issueKey: string) => {
    setSelectedIssues(prev => {
      const newSet = new Set(prev);
      if (newSet.has(issueKey)) {
        newSet.delete(issueKey);
      } else {
        newSet.add(issueKey);
      }
      return newSet;
    });
  };

  const handleSelectAll = () => {
    if (selectAllChecked) {
      // Deselect all issues on current page
      setSelectedIssues(prev => {
        const newSet = new Set(prev);
        for (const issue of sonarIssues) {
          newSet.delete(issue.key);
        }
        return newSet;
      });
    } else {
      // Select all issues on current page
      setSelectedIssues(prev => {
        const newSet = new Set(prev);
        for (const issue of sonarIssues) {
          newSet.add(issue.key);
        }
        return newSet;
      });
    }
  };

  // AI prompt generation handler function
  const handleGenerateAIPrompt = async () => {
    const selectedIssueKeys = Array.from(selectedIssues);
    console.log('Selected issue keys:', selectedIssueKeys);

    try {
      const response = await fetch(`/cqm/sonarqube/projects/${projectID}/ai-prompt`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || '',
        },
        body: JSON.stringify({
          issueKeys: selectedIssueKeys
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const aiPromptData = await response.json();

      // Create a new window/tab to display the AI prompt
      const newWindow = globalThis.open('', '_blank');
      if (newWindow) {
        // Use modern DOM manipulation instead of document.write
        const doc = newWindow.document;
        
        // Set document title
        doc.title = `AI Fix Prompt - ${aiPromptData.issueCount} Issues`;
        
        // Create and append style element
        const style = doc.createElement('style');
        style.textContent = `
          body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
          pre { background: #f5f5f5; padding: 15px; border-radius: 5px; overflow-x: auto; }
          .header { background: #e3f2fd; padding: 15px; border-radius: 5px; margin-bottom: 20px; }
          .copy-btn { background: #1976d2; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin: 10px 0; }
          .copy-btn:hover { background: #1565c0; }
        `;
        doc.head.appendChild(style);
        
        // Create header div
        const headerDiv = doc.createElement('div');
        headerDiv.className = 'header';
        headerDiv.innerHTML = `
          <h1>AI Fix Prompt Generated</h1>
          <p><strong>Project:</strong> ${aiPromptData.projectInfo.name}</p>
          <p><strong>Issues:</strong> ${aiPromptData.issueCount}</p>
          <p><strong>Generated:</strong> ${new Date(aiPromptData.generatedAt).toLocaleString()}</p>
        `;
        doc.body.appendChild(headerDiv);
        
        // Create copy button
        const copyBtn = doc.createElement('button');
        copyBtn.className = 'copy-btn';
        copyBtn.textContent = 'Copy Prompt to Clipboard';
        copyBtn.onclick = () => {
          const content = promptContent.textContent;
          copyToClipboard(content || '');
        };
        doc.body.appendChild(copyBtn);
        
        // Create pre element for prompt content
        const promptContent = doc.createElement('pre');
        promptContent.id = 'prompt-content';
        promptContent.textContent = aiPromptData.prompt;
        doc.body.appendChild(promptContent);
      } else {
        // Fallback if popup is blocked
        alert('AI prompt generated successfully! Check the console for details.');
        console.log('AI Prompt:', aiPromptData.prompt);
      }

    } catch (error) {
      console.error('Failed to generate AI prompt:', error);
      alert(`Failed to generate AI prompt: ${error.message}`);
    }
  };

  // Clear selection handler
  const handleClearSelection = () => {
    setSelectedIssues(new Set());
  };

  // Function to format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  // Function to get severity badge class
  const getSeverityBadgeClass = (severity: string) => {
    switch (severity.toLowerCase()) {
      case 'blocker':
        return 'badge-error';
      case 'critical':
        return 'badge-error';
      case 'major':
        return 'badge-warning';
      case 'minor':
        return 'badge-info';
      case 'info':
        return 'badge-ghost';
      default:
        return 'badge-secondary';
    }
  };

  // Function to handle creating Jira issue
  const handleCreateJiraIssue = (issueKey: string) => {
    // Set loading state for this specific issue
    setLoadingIssues(prev => ({ ...prev, [issueKey]: true }));
    
    router.post(
      `/cqm/sonarqube/projects/${projectID}/issues/${issueKey}`,
      {},
      {
        only: ['errors', 'updatedIssues'],
        onSuccess: (opts) => {
          console.log('Issue created successfully:', opts);
        },
        onFinish: (opts) => {
          console.log('Issue created Finished', opts);
          setSonarIssues(issues);
          // Remove loading state when finished
          setLoadingIssues(prev => ({ ...prev, [issueKey]: false }));
        },
        onError: (errors) => {
          console.error('Error:', errors);
          // Remove loading state on error too
          setLoadingIssues(prev => ({ ...prev, [issueKey]: false }));
        },
      }
    );
  };

  return (
    <div className="container mx-auto p-4">
      <Breadcrumbs 
        items={[
          { title: 'Home', href: '/' },
          { title: 'SonarQube Projects', href: '/cqm/sonarqube/projects' },
          { title: 'Issues' }
        ]} 
      />
      <h1 className="text-2xl font-bold mb-6">Jira project: {jiraProject.name} ({jiraProject.projectKey})</h1>
      <h2 className="text-1xl font-bold mb-6">SonarQube issues ({pagination.totalCount})</h2>

      {/* Selection Summary Bar */}
      {selectedIssues.size > 0 && (
        <div className="bg-base-200 p-4 rounded-lg mb-4 flex justify-between items-center">
          <div className="flex items-center gap-4">
            <span className="font-medium">
              {selectedIssues.size} issue{selectedIssues.size === 1 ? '' : 's'} selected
            </span>
            <button
              onClick={handleClearSelection}
              className="btn btn-sm btn-ghost"
            >
              Clear Selection
            </button>
          </div>
          <button
            onClick={handleGenerateAIPrompt}
            className="btn btn-primary"
            disabled={selectedIssues.size === 0}
          >
            Generate AI Fix Prompt ({selectedIssues.size})
          </button>
        </div>
      )}

      {/* Error Rendering */}
      {errorsState.length > 0 && (
        <div className="alert alert-error mt-4">
          <ul>
            {errorsState.map((error, index) => (
              <li key={`error-${error}-${index}`}>{error}</li>
            ))}
          </ul>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="table table-zebra w-full">
          <thead>
            <tr className="bg-base-200">
              <th className="w-12">
                <input
                  type="checkbox"
                  className="checkbox checkbox-sm"
                  checked={selectAllChecked}
                  onChange={handleSelectAll}
                  aria-label="Select all issues"
                />
              </th>
              <th className="text-left">Severity</th>
              <th className="text-left">Message</th>
              <th className="text-left">Effort</th>
              <th className="text-left">Status</th>
              <th className="text-left">Author</th>
              <th className="text-left">Tags</th>
              <th className="text-left">Updated</th>
              <th className="text-left">Jira Issue</th>
            </tr>
          </thead>
          <tbody>
            {sonarIssues.map((issue: Issue) => (
              <tr key={issue.key} className={`hover ${selectedIssues.has(issue.key) ? 'bg-primary/10' : ''}`}>
                <td>
                  <input
                    type="checkbox"
                    className="checkbox checkbox-sm"
                    checked={selectedIssues.has(issue.key)}
                    onChange={() => handleIssueSelect(issue.key)}
                    aria-label={`Select issue: ${issue.message}`}
                  />
                </td>
                <td>
                  <span className={`badge ${getSeverityBadgeClass(issue.severity)}`}>
                    {issue.severity}
                  </span>
                </td>
                <td className="max-w-md truncate">
                  
                  <a className="" target="_blank" href={`${issue.sonarURL}`}>
                    {issue.message}
                  </a>
                </td>
                <td className="font-mono text-xs truncate max-w-xs">
                  {issue.effort === '' ? '--' : issue.effort}
                </td>
                <td>
                  <span className={`badge ${issue.status === 'OPEN' ? 'badge-warning' : 'badge-success'}`}>
                    {issue.status}
                  </span>
                </td>
                <td>{issue.author === '' ? '--' : issue.author}</td>
                <td>{issue.tags}</td>
                <td>{formatDate(issue.updateDate)}</td>
                <td>

                  {issue.jiraIssueKey ? (
                    <a className="btn btn-sm btn-secondary" target="_blank" href={`${issue.url}`}>{issue.jiraIssueKey}</a>
                  ) : (
                    <button
                      onClick={() => handleCreateJiraIssue(issue.key)}
                      className="btn btn-sm btn-primary"
                      disabled={loadingIssues[issue.key]}
                    >
                      {loadingIssues[issue.key] ? 'Creating...' : 'Create'}
                    </button>
                  )}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
        
      </div>

      <div className="flex justify-center mt-4">
          <Paginator
            currentPage={currentPage}
            totalItems={pagination.totalCount}
            itemsPerPage={pagination.perPage}
            firstNumberOfPages={3}
            lastNumberOfPages={1}
            onPageChange={(nextPage: number) => {
              router.get(`/cqm/sonarqube/projects/${projectID}/issues`, { page: nextPage }, {
                only: ['issues', 'errors'],
                preserveState: true,
                preserveScroll: true
              })
              setCurrentPage(nextPage)
            }}
          />
        </div>

      {sonarIssues.length === 0 && (
        <div className="alert alert-info mt-4">
          <div className="flex-1">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" className="w-6 h-6 mx-2 stroke-current">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <span>No issues found.</span>
          </div>
        </div>
      )}

    </div>
  );
}
