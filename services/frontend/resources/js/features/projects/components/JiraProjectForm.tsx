import React, { useEffect, useState, useMemo } from 'react';
import { useForm, router } from '@inertiajs/react';
import { CompanyClient, JiraProject } from '@/Types/sonarProjects';
import Button from '@/components/Button';
import TextFieldNew from '@/components/fields/TextField/TextFieldNew';
import { PencilIcon, PlusIcon } from '@heroicons/react/24/outline';
import Select from '@/components/fields/Select/Select';
import CompanyClientForm from './CompanyClientForm';
import { useDebounce } from '@/utils/debounce';
import Checkbox from '@/components/fields/Checkbox/Checkbox';

interface JiraProjectFromProps {
  defaultURL: string;
  jiraProject?: JiraProject;
  onCreate?: (project: JiraProject) => void;
  onUpdate?: (project: JiraProject) => void;
  open?: boolean;
  onClose?: () => void;
}

const JiraProjectForm = (props: JiraProjectFromProps) => {
  const { defaultURL, jiraProject, onCreate, onUpdate, open, onClose } = props;
  const { data, setData, post, processing, reset, errors, put } = useForm({
    projectKey: '',
    username: '',
    token: '',
    companyClientId: '',
    jiraURL: defaultURL,
    active: true,
  });
  const isFormValid = useMemo(() => {
    const value = data.projectKey.trim() !== '' &&
      data.username.trim() !== '' &&
      (jiraProject || data.token.trim() !== '') &&
      data.jiraURL.trim() !== '' &&
      data.companyClientId.trim() !== '';
    return value;
  }, [data.projectKey, data.username, data.token, data.jiraURL, data.companyClientId, jiraProject]);

  const [companyClientSearch, setCompanyClientSearch] = useState('');
  const [companyClientSelected, setCompanyClientSelected] = useState(false);
  const [companyClient, setCompanyClient] = useState<CompanyClient | null>(jiraProject?.companyClient || null);
  const [companyClients, setCompanyClients] = useState<CompanyClient[]>([]);
  const debouncedSearch = useDebounce(companyClientSearch, 500);

  const closeProjectForm = (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.preventDefault();
    onClose?.();
    reset();
  };

  const clientSelected = (client: CompanyClient) => {
    setData('companyClientId', `${client?.id || ''}`);
    setCompanyClientSelected(true);
    setCompanyClient(client);
  };

  const submitProjectForm = (event: React.FormEvent) => {
    event.preventDefault();
    if (jiraProject) {
      put(`/cqm/jira/projects/${jiraProject.id}`, {
        only: ['errors', 'updatedJiraProject'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        onSuccess: (opts) => {
          closeProjectForm();
          onUpdate?.(opts.props.updatedJiraProject as JiraProject);
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    } else {
      post(`/cqm/jira/projects`, {
        only: ['errors', 'createdJiraProject'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        onSuccess: (opts) => {
          closeProjectForm();
          onCreate?.(opts.props.createdJiraProject as JiraProject);
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    }
  };

  const updateCompanyClients = (search: string) => {
    router.get(
      '/cqm/company/clients',
      { name: search },
      {
        only: ['companyClients', 'companyClientsPagination'],
        preserveState: true,
        replace: true,
        preserveUrl: true,
        onSuccess: (response) => {
          const clients = response.props.companyClients as CompanyClient[];
          setCompanyClients(clients);
        },
        onError: (errors) => { },
      }
    );
  };

  useEffect(() => {
    updateCompanyClients(debouncedSearch);
  }, [debouncedSearch]);

  useEffect(() => {
    if (open) {
      document.getElementById('jiraProjectFormContainer').classList.remove('hidden');
      document.getElementById('jiraProjectFormContainer').classList.add('flex');
    }
  }, [open]);

  // Set the form data if jiraProject is provided
  useEffect(() => {
    setData('projectKey', jiraProject?.projectKey || '');
    setData('jiraURL', jiraProject?.jiraURL || defaultURL);
    setData('username', jiraProject?.username || '');
    setData('active', jiraProject?.active || true);
    setData('token', '');
    setCompanyClientSelected(jiraProject !== null);
    setCompanyClient(jiraProject?.companyClient);
  }, [jiraProject]);

  const showCompanyClientForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setCompanyClient(null);
    setCompanyClientSelected(null);
    if (companyClient) {
      setData('companyClientId', '');
    }
    document.getElementById('companyClientForm').classList.add('flex');
    document.getElementById('companyClientForm').classList.remove('hidden');
  };

  const showEditCompanyClientForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    document.getElementById('companyClientForm').classList.add('flex');
    document.getElementById('companyClientForm').classList.remove('hidden');
  };

  const clientSubmitted = (client: CompanyClient, update = false) => {
    clientSelected(client);
    if (update) {
      setCompanyClients((prevClients) => prevClients.map((c) => (c.id === client.id ? client : c)));
    } else {
      setCompanyClients((prevClients) => [...prevClients, client]);
    }
  };

  useEffect(() => {
    document.getElementById('jiraProjectFormContainer').classList.remove('hidden');
    document.getElementById('jiraProjectFormContainer').classList.add('flex');
  }, []);
  
  useEffect(() => {
    if (companyClient) {
      setData('companyClientId', `${companyClient.id}`);
    }
  }, [companyClient]);

  return (
    <div className='hidden flex-col gap-2 mb-10 p-2 border rounded-lg w-full' id='jiraProjectFormContainer'>
      <div className='flex flex-row items-center gap-3 mb-2'>
        <form className='flex flex-col gap-2 w-1/2' id='jiraProjectForm' onSubmit={(event: React.FormEvent) => submitProjectForm(event)}>
          <TextFieldNew
            label={'Project Key'}
            name={'projectKey'}
            id={'jiraProjectKey'}
            placeholder={'Jira Project Key'}
            className='w-full'
            required={true}
            errors={[errors.projectKey]}
            value={data.projectKey}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('projectKey', e.target.value)}
          />
          <TextFieldNew
            label={'Jira URL'}
            name={'jiraURL'}
            placeholder={'Jira URL'}
            className='w-full'
            required={true}
            errors={[errors.jiraURL]}
            value={data.jiraURL}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('jiraURL', e.target.value)}
          />
          <TextFieldNew
            label='Username'
            name='username'
            placeholder='Email'
            className='w-full'
            type='email'
            value={data.username}
            errors={[errors.username]}
            required={true}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('username', e.target.value)}
          />
          <TextFieldNew
            label='Token'
            name='token'
            placeholder='User Token'
            className='w-full'
            type='password'
            errors={[errors.token]}
            value={data.token}
            validations={jiraProject ? {} : { required: true }}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('token', e.target.value)}
          />
          <Checkbox checked={data.active} label='Active' name={'active'} inputProps={{className: 'order-2'}} labelProps={{className: 'order-1'}} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('active', e.target.checked)}></Checkbox>
        </form>
        <div className='border border-gray-300 border-solid h-auto' />
        <div className='flex flex-col self-start gap-2 w-1/2'>
          <h3 className='font-bold'>
            Company Client<span className='text-red-500'>*</span>
          </h3>
          <p className='font-semibold text-gray-700 text-sm'>Select an existing Company Client or create a new one.</p>
          <div className='flex flex-row gap-2 w-full'>
            <div className='w-full'>
              <Select<CompanyClient>
                options={companyClients}
                multiple={false}
                onSelectOption={(client) => setCompanyClient(client)}
                placeholder={'Select Client'}
                value={companyClient ? [companyClient] : []}
                onInputChange={(value) => setCompanyClientSearch(value)}
                setValue={(clients) => clientSelected(clients[0] as CompanyClient)}
                errors={[errors.companyClientId]}
              />
            </div>
            <div className='flex gap-2'>
              <Button
                icon={<PlusIcon className='w-4 h-4 text-white' />}
                tooltip='Create Client'
                className='btn-primary'
                onClick={(event: React.MouseEvent<HTMLButtonElement>) => showCompanyClientForm(event)}
              />
              {companyClient && companyClientSelected && (
                <Button
                  icon={<PencilIcon className='w-4 h-4 text-white' />}
                  tooltip='Edit Client'
                  className='btn-secondary'
                  onClick={(event: React.MouseEvent<HTMLButtonElement>) => showEditCompanyClientForm(event)}
                />
              )}
            </div>
          </div>
          <CompanyClientForm
            companyClient={companyClient}
            onCreate={(client) => clientSubmitted(client, false)}
            onUpdate={(client) => clientSubmitted(client, true)}
          />
        </div>
      </div>
      <div className='flex flex-row justify-end gap-2 mt-4 w-full'>
        <Button
          label='Close'
          className='btn-secondary shrink'
          onClick={(event: React.MouseEvent<HTMLButtonElement>) => closeProjectForm(event)}
        />
        <Button
          label={jiraProject ? 'Update' : 'Save'}
          loadingLabel={jiraProject ? 'Updating...' : 'Saving...'}
          processing={processing}
          disabled={!isFormValid}
          className='btn-primary'
          type='submit'
          form='jiraProjectForm'
        />
      </div>
    </div>
  );
};
export default JiraProjectForm;
