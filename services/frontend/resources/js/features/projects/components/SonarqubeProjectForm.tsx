import TextFieldNew from '@/components/fields/TextField/TextFieldNew';
import React, { useState, useEffect, useRef } from 'react';
import { useForm, router } from '@inertiajs/react';
import JiraProjectForm from './JiraProjectForm';
import { JiraProject, Project } from '@/Types/sonarProjects';
import Button from '@/components/Button';
import { PencilIcon, PlusIcon } from '@heroicons/react/24/outline';
import Select from '@/components/fields/Select/Select';
import { useDebounce } from '@/utils/debounce';
import Checkbox from '@/components/fields/Checkbox/Checkbox';

interface SonarFormProps {
  defaultURL: string;
  sonarProject?: Project;
  onClose?: () => void;
  onUpdate?: (project: Project) => void;
}

const SonarQubeProjectForm = (props: SonarFormProps) => {
  const { sonarProject, defaultURL, onClose, onUpdate } = props;
  const [jiraProject, setJiraProject] = useState<JiraProject | null>(sonarProject?.jiraProject || null);
  const { data, setData, processing, reset, post, errors, put } = useForm({ projectKey: '', branch: '', jiraProjectId: '', active: true as boolean });
  const isFormValid = data.projectKey.trim() !== '' && data.branch.trim() !== '' && data.jiraProjectId.trim() !== '';
  const [jiraSelected, setJiraSelected] = useState(false);
  const [jiraProjects, setJiraProjects] = useState<JiraProject[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const debouncedSearch = useDebounce(searchValue, 500);
  const dialogRef = useRef<HTMLDialogElement>(null);
  const [isJiraFormOpen, setIsJiraFormOpen] = useState(false);

  const submitProjectForm = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
    if (sonarProject) {
      put(`/cqm/sonarqube/projects/${sonarProject.id}`, {
        only: ['errors', 'updatedSonarProject'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        errorBag: 'sonarProjectForm',
        onSuccess: (opts) => {
          closeSonarForm();
          const modal = document.getElementById('projectFormModal') as HTMLDialogElement;
          modal.close();
          onUpdate?.(opts.props.updatedSonarProject as Project);
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    } else {
      post(`/cqm/sonarqube/projects`, {
        only: ['errors'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        errorBag: 'sonarProjectForm',
        onSuccess: (opts) => {
          closeSonarForm();
          const modal = document.getElementById('projectFormModal') as HTMLDialogElement;
          modal.close();
          router.reload({ only: ['sonarProjects', 'sonarProjectsPagination'] });
          console.log('Sonar Project created successfully:');
        },
        onError: (errors) => {
          console.error('Error:', errors);
        },
      });
    }
  };

  const closeSonarForm = (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.preventDefault();
    setIsJiraFormOpen(false);
    const modal = document.getElementById('projectFormModal') as HTMLDialogElement;
    setJiraSelected(null);
    setJiraProject(null);
    modal.close();
    reset();
    onClose?.();
  };

  useEffect(() => {
    if (sonarProject) {
      setData('projectKey', sonarProject.projectKey);
      setData('branch', sonarProject.branch);
      setData('jiraProjectId', sonarProject.jiraProject.id);
      setData('active', sonarProject.active);
      setJiraSelected(true);
      setJiraProject(sonarProject.jiraProject);
    } else {
      setJiraSelected(false);
    }
  }, [sonarProject]);

  const showJiraProjectForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setJiraProject(null);
    setJiraSelected(null);
    if (sonarProject) {
      setData('jiraProjectId', '');
    }
    setIsJiraFormOpen(true);
  };

  const showEditJiraProjectForm = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
    setIsJiraFormOpen(true);
  };

  const jiraProjectSelected = (jiraProject: JiraProject) => {
    setData('jiraProjectId', `${jiraProject?.id || ''}`);
    setJiraSelected(true);
    setJiraProject(jiraProject);
  };

  const projectSubmitted = (project: JiraProject, update = false) => {
    jiraProjectSelected(project);
    if (update) {
      setJiraProjects((prevProjects) => prevProjects.map((p) => (p.id === project.id ? project : p)));
    } else {
      setJiraProjects((prevProjects) => [...prevProjects, project]);
    }
  };

  const fetchJiraProjects = (query: string) => {
    router.get(
      '/cqm/jira/projects',
      { name: query },
      {
        only: ['jiraProjects'],
        preserveState: true,
        replace: true,
        preserveUrl: true,
        onSuccess: (response) => {
          const projects = response.props.jiraProjects as JiraProject[];
          setJiraProjects(projects);
        },
        onError: (errors) => {
          console.error('Error fetching Jira projects:', errors);
        },
      }
    );
  };

  useEffect(() => {
    fetchJiraProjects(debouncedSearch);
  }, [debouncedSearch]);

  useEffect(() => {
    if (dialogRef.current && !dialogRef.current.open) {
      dialogRef.current.show();
    }
  }, []);

  return (
    <dialog id='projectFormModal' className='!z-10 modal' ref={dialogRef}>
      <div className='!max-w-6xl overflow-y-visible modal-box'>
        <h3 className='font-bold text-lg'>Sonar Project</h3>
        <div className='flex flex-col gap-2'>
          <form className='flex flex-col gap-2 w-full' onSubmit={(event: React.FormEvent) => submitProjectForm(event)} id='sonarForm'>
            <div className='flex flex-row gap-2 mb-2 w-full'>
              <TextFieldNew
                label='Sonar Project Key'
                name='projectKey'
                placeholder='Sonar Project Key'
                errors={[errors.projectKey]}
                className='w-1/2'
                value={data.projectKey}
                required={true}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('projectKey', e.target.value)}
              />
              <TextFieldNew
                label='Branch'
                name='branch'
                placeholder='Branch to be analyzed'
                className='w-1/2'
                errors={[errors.branch]}
                value={data.branch}
                required={true}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('branch', e.target.value)}
              />
            </div>
            <div className='flex flex-row gap-2 mb-2 w-full'>
              <Checkbox checked={data.active} label='Active' name={'active'} inputProps={{className: 'order-2'}} labelProps={{className: 'order-1'}} onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('active', e.target.checked)}></Checkbox>
            </div>
            <h3 className='font-bold'>
              Jira Project<span className='text-red-500'>*</span>
            </h3>
            <p className='font-semibold text-gray-700 text-sm'>Select an existing Jira project or create a new one.</p>
            {jiraSelected && jiraProject && <p className='font-semibold text-blue-500 text-sm'>Selected: {jiraProject.name}</p>}
            <div className='flex flex-row items-start gap-2 w-full'>
              <div className='w-1/2'>
                <Select<JiraProject>
                  autoComplete={false}
                  options={jiraProjects}
                  onSelectOption={(project) => setJiraProject(project)}
                  multiple={false}
                  placeholder={'Select Jira Project'}
                  onInputChange={setSearchValue}
                  errors={[errors.jiraProjectId]}
                  value={jiraProject ? [jiraProject] : []}
                  setValue={(projects) => jiraProjectSelected(projects[0] as JiraProject)}
                />
              </div>

              <div className='flex gap-2 w-1/2'>
                <Button
                  icon={<PlusIcon className='w-4 h-4 text-white' />}
                  tooltip='Create Jira Project'
                  className='btn-primary'
                  onClick={(event: React.MouseEvent<HTMLButtonElement>) => showJiraProjectForm(event)}
                />
                {jiraSelected && jiraProject && (
                  <Button
                    icon={<PencilIcon className='w-4 h-4 text-white' />}
                    tooltip='Edit Jira Project'
                    className='btn-secondary'
                    onClick={(event: React.MouseEvent<HTMLButtonElement>) => showEditJiraProjectForm(event)}
                  />
                )}
              </div>
            </div>
          </form>
          {isJiraFormOpen && (
            <JiraProjectForm
              defaultURL={defaultURL}
              jiraProject={jiraProject}
              onCreate={(jiraProject: JiraProject) => projectSubmitted(jiraProject)}
              onUpdate={(jiraProject: JiraProject) => projectSubmitted(jiraProject, true)}
              onClose={() => {
                setIsJiraFormOpen(false);
              }}
            />
          )}
          <div className='flex flex-row justify-end gap-2'>
            <Button
              label='Close'
              className='btn-secondary'
              onClick={(event: React.MouseEvent<HTMLButtonElement>) => closeSonarForm(event)}
            />
            <Button
              label={sonarProject ? 'Update' : 'Save'}
              loadingLabel={sonarProject ? 'Updating...' : 'Saving...'}
              processing={processing}
              disabled={!isFormValid}
              className='btn-primary'
              type='submit'
              form='sonarForm'
            />
          </div>
        </div>
      </div>
    </dialog>
  );
};
export default SonarQubeProjectForm;
