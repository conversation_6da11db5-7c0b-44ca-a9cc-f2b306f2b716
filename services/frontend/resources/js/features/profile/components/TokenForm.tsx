import TextFieldNew from '@/components/fields/TextField/TextFieldNew';
import React, { useEffect, useRef } from 'react';
import { useForm, router } from '@inertiajs/react';
import Button from '@/components/Button';

interface TokenFormProps {
  onClose?: () => void;
}

const TokenForm = (props: TokenFormProps) => {
  const { onClose } = props;
  const { data, setData, processing, reset, post, errors } = useForm({ expiresAt: '' });
  const isFormValid = data.expiresAt.trim() !== '';
  const dialogRef = useRef<HTMLDialogElement>(null);

  const submitTokenForm = (event: React.MouseEvent<HTMLElement>) => {
    event.preventDefault();
      post(`/profile/tokens`, {
        only: ['errors'],
        preserveState: true,
        preserveUrl: true,
        replace: true,
        errorBag: 'tokenForm',
        onSuccess: (opts) => {
          closeTokenForm();
          const modal = document.getElementById('tokenFormModal') as HTMLDialogElement;
          modal.close();
          router.reload({ only: ['tokens', 'tokensPagination'] });
          console.log('Token created successfully:');
        },
        onError: (formError) => {
          console.log('Error:', formError);
        },
      });
    }

  const closeTokenForm = (event?: React.MouseEvent<HTMLButtonElement>) => {
    event?.preventDefault();
    const modal = document.getElementById('tokenFormModal') as HTMLDialogElement;
    modal.close();
    reset();
    onClose?.();
  };


  useEffect(() => {
    if (dialogRef.current && !dialogRef.current.open) {
      dialogRef.current.show();
    }
  }, []);

  return (
    <dialog id='tokenFormModal' className='!z-10 modal' ref={dialogRef}>
      <div className='!max-w-6xl overflow-y-visible modal-box'>
        <h3 className='font-bold text-lg'>Token</h3>
        <div className='flex flex-col gap-2'>
          <form className='flex flex-col gap-2 w-full' onSubmit={(event: React.FormEvent) => submitTokenForm(event)} id='tokenForm'>
            <div className='flex flex-row gap-2 mb-2 w-full'>
              <TextFieldNew
                label='Expires At'
                name='projectKey'
                type='date'
                placeholder='Expires At'
                errors={[errors.expiresAt]}
                className='w-1/2'
                value={data.expiresAt}
                required={true}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setData('expiresAt', e.target.value)}
              />
            </div>
          </form>
         
          <div className='flex flex-row justify-end gap-2'>
            <Button
              label='Close'
              className='btn-secondary'
              onClick={(event: React.MouseEvent<HTMLButtonElement>) => closeTokenForm(event)}
            />
            <Button
              label="Save"
              loadingLabel="Saving..."
              processing={processing}
              disabled={!isFormValid}
              className='btn-primary'
              type='submit'
              form='tokenForm'
            />
          </div>
        </div>
      </div>
    </dialog>
  );
};
export default TokenForm;
