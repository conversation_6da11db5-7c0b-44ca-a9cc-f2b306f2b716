import React from "react"
import Sidebar from "./Sidebar"
import Header from "./Header"
import Footer from "./Footer"

export default function Layout({ children }) {
  return (
    <div>
      <div className="drawer lg:drawer-close">
      <input id="my-drawer" type="checkbox" className="drawer-toggle" aria-label="Toggle sidebar navigation" />
      <div className="drawer-content">
        <div className="min-h-screen flex flex-col">
          <Header />
          <main className="flex-grow container mx-auto px-4 py-8">{children}</main>
          <Footer />
        </div>
        </div>
        <Sidebar />
      </div>
    </div>
  )
}
