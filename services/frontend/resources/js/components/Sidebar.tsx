import React, { useState, useEffect } from "react"
import { Link, router } from '@inertiajs/react'

const Sidebar: React.FC = () => {
  const [currentPath, setCurrentPath] = useState(globalThis.location.pathname);
  
  // Update current path whenever the URL changes
  useEffect(() => {
    const updatePath = () => {
      setCurrentPath(globalThis.location.pathname);
    };
    
    // Listen for Inertia navigation events
    router.on('navigate', updatePath);
    
    // Clean up
    return () => {
    };
  }, []);
  
  // Function to determine if a link is active
  const isActiveClass = (path: string, exact: boolean = false) => {
    if (exact) {
      return currentPath === path ? 'active' : '';
    }
    return currentPath.startsWith(path) ? 'active' : '';
  };
  
  // Function to close the drawer
  const closeDrawer = () => {
    // Get the drawer checkbox input
    const drawerCheckbox = document.getElementById('my-drawer') as HTMLInputElement;
    if (drawerCheckbox) {
      drawerCheckbox.checked = false;
    }
  };

  return (
    <div className="drawer-side">
      <label htmlFor="my-drawer" className="drawer-overlay" aria-label="Close navigation menu"></label>
      <ul className="bg-base-200 p-4 w-80 min-h-full text-base-content menu">
        <li className="mb-2">
          <Link href="/" className={isActiveClass('/', true)} onClick={closeDrawer}>
            Home
          </Link>
        </li>
        <li className="mb-2">
          <a href="/wiki" className={isActiveClass('/wiki')} onClick={closeDrawer}>
            Wiki
          </a>
        </li>
        <li className="mb-2">
          <Link href="/cqm/sonarqube/projects" className={isActiveClass('/cqm/sonarqube/projects')} onClick={closeDrawer}>
            SonarQube Projects
          </Link>
        </li>
        <li className="mb-2">
          <Link href="/admin" className={isActiveClass('/admin')} onClick={closeDrawer}>
            Admin
          </Link>
        </li>
        <li className="mb-2">
          <Link href="/profile" className={isActiveClass('/profile')} onClick={closeDrawer}>
            Profile
          </Link>
        </li>
        <li className="mt-auto">
          <a href="/logout" className="" onClick={closeDrawer}>
            Logout
          </a>
        </li>
      </ul>
    </div>
  )
}

export default Sidebar

