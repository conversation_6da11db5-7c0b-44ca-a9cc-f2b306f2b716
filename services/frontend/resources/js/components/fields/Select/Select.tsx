import React, { useRef, useState, useEffect } from 'react';
import type { ChangeEvent } from 'react';
import { SelectMultipleProps, SelectOption } from './select-types';
import TextFieldNew from '../TextField/TextFieldNew';
import { XMarkIcon } from '@heroicons/react/24/outline';
import Checkbox from '../Checkbox/Checkbox';

export default function Select<TData>(props: SelectMultipleProps<TData>) {
  const {
    value = [],
    setValue,
    autoComplete = false,
    options: data = [],
    onSelectOption,
    loading,
    multiple,
    placeholder,
    onClearAll,
    transformOptionName,
    onInputChange: onInputChangeFn,
    ...restOfProps
  } = props;

  const [open, setOpen] = useState(false);
  const [inputValue, setInputValue] = useState<string>('');
  const popoverRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (popoverRef.current && !popoverRef.current.contains(event.target as Node)) {
        setOpen(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const onInputClick = () => {
    // Open dropdown unless it's a single select that already has a value
    if (multiple || !value?.length) {
      setOpen(true);
    }
  };

  const onInputChange = (event: ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(event.target.value);
    onInputChangeFn?.(event.target.value);
  };

  const removeItem = (item: SelectOption<TData>) => {
    setValue?.(value?.filter?.((i) => i.name !== item.name));
  };

  const sideAdornment = () => {
    return Array.isArray(value)
      ? value.map((item) => {
          const onChipClick = () => {
            removeItem(item);
          };

          return (
            <div
              title={item.name}
              key={item.name}
              className={`flex ${multiple ? 'max-w-36' : ''} justify-between gap-1 rounded-lg bg-gray-200 px-2 py-1 text-caption-bold`}
            >
              <div className={`${multiple ? 'line-clamp-1' : 'whitespace-nowrap'} text-left`}>
                {transformOptionName?.(item) || item.name}
              </div>

              <button type='button' onClick={onChipClick} className='flex shrink-0'>
                <XMarkIcon className='w-5 h-5' />
              </button>
            </div>
          );
        })
      : undefined;
  };

  return (
    <div className='relative inline-block text-left w-full' ref={popoverRef}>
      <TextFieldNew
        {...restOfProps}
        onClick={onInputClick}
        disabled={!multiple && value?.length > 0}
        onChange={onInputChange}
        placeholder={placeholder}
        value={inputValue}
        containerProps={{ onClick: onInputClick }}
        sideAdornment={sideAdornment()}
      />

      {open && (
        <div className='absolute left-0 mt-2 w-full rounded-lg border border-gray-200 bg-white shadow-lg p-4 z-50 flex max-h-96 flex-col gap-1 shadow-table-border'>
          {data.length > 0 ? (
            data.map((option, index) => {
              const key = `select-option-${index}-${option.id}`;
              const label = transformOptionName?.(option) || option.name;
              const selected = value?.some?.((item) => item.id === option.id);

              const onClick = () => {
                onSelectOption?.(option);
                setInputValue('');

                const internalSelected = value?.filter?.((item) => item.id !== option.id) || [];

                let internalValue = [];

                if (multiple) {
                  internalValue = selected ? internalSelected : [...internalSelected, option];
                } else {
                  internalValue = selected ? [] : [option];
                }

                setValue?.(internalValue);

                if (!multiple && internalValue?.length > 0) {
                  setOpen(false);
                }
              };

              return (
                <button
                  type='button'
                  key={key}
                  onClick={onClick}
                  className='optionStyle'
                  value={option.id}
                  disabled={!multiple && value?.length > 0}
                >
                  <Checkbox checked={selected} label={label} readOnly={true} />
                </button>
              );
            })
          ) : (
            <div className='empty'>Empty</div>
          )}
        </div>
      )}
    </div>
  );
}
