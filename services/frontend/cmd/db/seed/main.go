package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"sa-intranet/core"
	"sa-intranet/usecase/cqm/model"

	intranet "sa-intranet"
	intranetConfig "sa-intranet/config"

	"github.com/google/uuid"
	"github.com/samber/do"
	"github.com/uptrace/bun"
)

const (
	uuidParseErrorFormat = "failed to parse UUID: %w"
)

func main() {
	injector := do.New()

	err := run(injector)
	if err != nil {
		panic(err)
	}
}

func run(i *do.Injector) error {
	conf, err := intranetConfig.NewConfig()
	if err != nil {
		return fmt.Errorf("failed to create config: %w", err)
	}

	if err = intranet.Register(i, *conf); err != nil {
		return fmt.Errorf("failed to register dependencies: %w", err)
	}

	// uuid, err := uuid.NewV7()
	// if err != nil {
	// 	return fmt.Errorf("failed to generate UUID: %w", err)
	// }

	// log.Printf("Generated UUID: %s\n", uuid.String())

	return runLocalSeed(i)
}

func runLocalSeed(i *do.Injector) error {
	ctx := context.Background()

	cypher, err := do.Invoke[core.Cypher](i)
	if err != nil {
		return err
	}

	db, err := do.Invoke[*bun.DB](i)
	if err != nil {
		return err
	}
	companyClientID, err := uuid.Parse("01962d72-c87d-7e7e-b4e9-e96605e8e4bf")
	if err != nil {
		return fmt.Errorf(uuidParseErrorFormat, err)
	}
	companyClient := &model.CompanyClient{
		ID:   companyClientID,
		Name: "My Client",
	}

	log.Println("Creating CompanyClient...")
	_, err = db.NewInsert().
		Model(companyClient).
		Ignore().
		Exec(ctx)
	if err != nil {
		log.Println(fmt.Errorf("failed to insert CompanyClient: %w", err).Error())
	}
	log.Println("CompanyClient created...")

	jiraProjectID, err := uuid.Parse("0195dda5-2a22-7659-8648-289e7d9f522d")
	if err != nil {
		return fmt.Errorf(uuidParseErrorFormat, err)
	}

	encryptedToken, err := cypher.Encrypt(os.Getenv("APP_DB_SEED_JIRA_TOKEN"))
	if err != nil {
		return fmt.Errorf("failed to encrypt token: %w", err)
	}

	jiraProject := &model.JiraProject{
		ID:              jiraProjectID,
		ProjectKey:      "BPL",
		Name:            "Backend Practice Leads",
		JiraURL:         os.Getenv("APP_JIRA_URL"),
		Token:           encryptedToken,
		Username:        os.Getenv("APP_DB_SEED_JIRA_USERNAME"),
		CompanyClientID: companyClientID,
	}

	log.Println("Creating JiraProject...")

	_, err = db.NewInsert().
		Model(jiraProject).
		Ignore().
		Exec(ctx)
	if err != nil {
		log.Println(fmt.Errorf("failed to insert JiraProject: %w", err).Error())
	}

	log.Println("JiraProject created...")

	sonarqubeProjectID, err := uuid.Parse("0195dda6-0db4-74a6-8d38-4464b32a8d85")
	if err != nil {
		return fmt.Errorf(uuidParseErrorFormat, err)
	}

	sonarqubeProject := &model.SonarqubeProject{
		ID:            sonarqubeProjectID,
		Branch:        "main",
		ProjectName:   "Test Project",
		ProjectKey:    os.Getenv("APP_DB_SEED_SONARQUBE_PROJECT_KEY"),
		JiraProjectID: jiraProjectID,
	}

	log.Println("Creating SonarQubeProject...")

	_, err = db.NewInsert().
		Model(sonarqubeProject).
		Ignore().
		Exec(ctx)
	if err != nil {
		log.Println(fmt.Errorf("failed to insert sonarqubeProject: %w", err).Error())
	}
	log.Println("SonarQubeProject created...")

	return nil
}
