package profile

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/core/cache"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"
	"sa-intranet/usecase/auth/service/out"
	"strconv"

	"app/frontend/internal/ui"

	httpmw "sa-intranet/http"

	"github.com/google/uuid"
	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

// Constants for repeated strings
const (
	httpErrorFormat   = "http error: %s\n"
	profileTokensPage = "Profile/Tokens"
)

func RegisterRoutes(app *ui.App) {
	baseRoute := "/profile"
	usecase := do.MustInvoke[*service.TokenService](app.Injector())
	cache := do.MustInvoke[cache.Cache](app.Injector())
	crtl := &Controller{
		inertia:   app.Inertia(),
		tokenCase: usecase,
		cache:     cache,
		config:    app.AppConfig,
	}

	app.Router().GET(baseRoute, nil, crtl.ProfileHandler())
	app.Router().GET(baseRoute+"/tokens", nil, crtl.TokensHandler())
	app.Router().POST(baseRoute+"/tokens", nil, crtl.CreateTokenHandler())
	app.Router().DELETE(baseRoute+"/tokens/{tokenID}", nil, crtl.DeleteTokenHandler())
}

type Controller struct {
	inertia   *inertia.Inertia
	tokenCase *service.TokenService
	cache     cache.Cache
	config    *ui.AppConfig
}

func (c *Controller) ProfileHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		// currentUser, ok := r.Context().Value("currentUser").(out.CurrentUserViewModel)
		// if !ok {
		// 	http.Error(w, "Unauthorized", http.StatusUnauthorized)
		// 	return
		// }

		// log.Printf("Current User: %s\n", currentUser.Email)

		err := i.Render(w, r, "Profile/Index", inertia.Props{
			// "currentUser": currentUser,
		})
		if err != nil {
			log.Printf(httpErrorFormat, err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) TokensHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		pageStr := r.URL.Query().Get("page")
		page := 1
		if pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil {
				page = p
			}
		}

		currentUser, ok := r.Context().Value(httpmw.CurrentUserKey).(out.CurrentUserViewModel)
		if !ok {
			http.Error(w, "Forbidden - not authenticated", http.StatusForbidden)
			return
		}
		input := in.ListTokensInput{
			UserID: currentUser.ID,
		}

		viewModel := c.tokenCase.ListTokens(input, page, c.config.Intranet.DefaultPageSize)
		errs := viewModel.Errors
		err := i.Render(w, r, profileTokensPage, inertia.Props{
			"tokens":           viewModel.Data.Items,
			"tokensPagination": viewModel.Data.Pagination,
			"roles":            viewModel.Data.Roles,
			"errors":           errs,
		})
		if err != nil {
			log.Printf(httpErrorFormat, err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) CreateTokenHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		currentUser, ok := r.Context().Value(httpmw.CurrentUserKey).(out.CurrentUserViewModel)
		if !ok {
			http.Error(w, "Not Found", http.StatusForbidden)
			return
		}

		props := inertia.Props{}

		var input in.CreateTokenInput

		if err := json.NewDecoder(r.Body).Decode(&input); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
		}

		input.UserID = currentUser.ID

		viewModel := c.tokenCase.CreateToken(input)

		if !viewModel.Success {
			props["errors"] = viewModel.Errors
		}
		err := i.Render(w, r, profileTokensPage, props)
		if err != nil {
			log.Printf(httpErrorFormat, err)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) DeleteTokenHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		currentUser, ok := r.Context().Value(httpmw.CurrentUserKey).(out.CurrentUserViewModel)
		if !ok {
			http.Error(w, "Not Found", http.StatusForbidden)
			return
		}

		tokenID := r.PathValue("tokenID")
		tokenUUID, er := uuid.Parse(tokenID)
		if er != nil {
			http.Error(w, "Invalid Token ID", http.StatusBadRequest)
			return
		}

		input := in.DeleteTokenInput{
			UserID: currentUser.ID,
			ID:     tokenUUID,
		}

		viewModel := c.tokenCase.DeleteToken(input)

		props := inertia.Props{}

		if !viewModel.Success {
			errs := viewModel.Errors
			props["errors"] = errs
		}

		err := i.Render(w, r, profileTokensPage, props)
		if err != nil {
			log.Printf(httpErrorFormat, err)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}
