package jira_projects

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/usecase/cqm/interactor"
	"strconv"

	"app/frontend/internal/ui"

	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

const (
	httpErrorFormatStr    = "http error: %s\n"
	projectsIndexTemplate = "CQM/Sonarqube/Projects/Index"
)

func RegisterRoutes(app *ui.App) {
	baseRoute := "/cqm/jira/projects"
	usecase := do.MustInvoke[*interactor.JiraProjectsInteractor](app.Injector())
	crtl := &Controller{
		inertia: app.Inertia(),
		usecase: usecase,
		config:  app.AppConfig,
	}

	app.Router().GET(baseRoute, nil, crtl.IndexHandler())
	app.Router().POST(baseRoute, nil, crtl.CreateJiraProject())
	app.Router().PUT(baseRoute+"/{projectID}", nil, crtl.UpdateJiraProject())
}

type Controller struct {
	inertia *inertia.Inertia
	usecase *interactor.JiraProjectsInteractor
	config  *ui.AppConfig
}

func (c *Controller) IndexHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		filter := r.URL.Query().Get("name")

		pageParam := r.URL.Query().Get("page")
		page := 1
		pageInt, err := strconv.Atoi(pageParam)
		if err == nil {
			page = pageInt
		}

		data, err := c.usecase.InitialData(filter, page, c.config.Intranet.DefaultPageSize)
		if err != nil {
			log.Printf(httpErrorFormatStr, err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)
			return
		}
		err = i.Render(w, r, projectsIndexTemplate, inertia.Props{
			"jiraProjects":           data.JiraProjects,
			"jiraProjectsPagination": data.Pagination,
		})
		if err != nil {
			log.Printf(httpErrorFormatStr, err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) CreateJiraProject() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		var data interactor.JiraProjectValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}
		errors := []string{}

		jiraProject, validationErrors, err := c.usecase.CreateJiraProject(data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), projectsIndexTemplate, inertia.Props{})
		} else {
			err = i.Render(w, r, projectsIndexTemplate, inertia.Props{
				"errors":             errors,
				"createdJiraProject": jiraProject,
			})
		}

		if err != nil {
			log.Printf(httpErrorFormatStr, err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) UpdateJiraProject() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		var data interactor.JiraProjectUpdateValidator
		if err := json.NewDecoder(r.Body).Decode(&data); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		projectID := r.PathValue("projectID")
		errors := []string{}

		jiraProject, validationErrors, err := c.usecase.UpdateJiraProject(projectID, data)
		if err != nil {
			errors = append(errors, err.Error())
		}
		if validationErrors != nil {
			errorsValidation := inertia.SetValidationErrors(r.Context(), validationErrors)
			err = i.Render(w, r.WithContext(errorsValidation), projectsIndexTemplate, inertia.Props{})
		} else {
			err = i.Render(w, r, projectsIndexTemplate, inertia.Props{
				"errors":             errors,
				"updatedJiraProject": jiraProject,
			})
		}

		if err != nil {
			log.Printf(httpErrorFormatStr, err)
		}
	}

	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}
