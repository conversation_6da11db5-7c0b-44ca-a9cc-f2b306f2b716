package users

import (
	"encoding/json"
	"log"
	"net/http"
	"sa-intranet/core/cache"
	"sa-intranet/usecase/auth/service"
	"sa-intranet/usecase/auth/service/in"
	"strconv"
	"strings"

	"app/frontend/internal/ui"

	"github.com/google/uuid"
	inertia "github.com/romsar/gonertia/v2"
	"github.com/samber/do"
)

func RegisterRoutes(app *ui.App) {
	baseRoute := "/admin/users"
	usecase := do.MustInvoke[*service.UserService](app.Injector())
	cache := do.MustInvoke[cache.Cache](app.Injector())
	crtl := &Controller{
		inertia:  app.Inertia(),
		usercase: usecase,
		cache:    cache,
		config:   app.AppConfig,
	}

	app.Router().GET(baseRoute, nil, crtl.IndexHandler())
	app.Router().PUT(baseRoute+"/{userID}/role", nil, crtl.UpdateRoleHandler())
}

type Controller struct {
	inertia  *inertia.Inertia
	usercase *service.UserService
	cache    cache.Cache
	config   *ui.AppConfig
}

func (c *Controller) IndexHandler() func(next http.Handler) http.Handler {
	i := c.inertia

	fn := func(w http.ResponseWriter, r *http.Request) {
		pageStr := r.URL.Query().Get("page")
		page := 1
		if pageStr != "" {
			if p, err := strconv.Atoi(pageStr); err == nil {
				page = p
			}
		}
		input := in.ListUsersInput{
			Email: strings.TrimSpace(r.URL.Query().Get("email")),
		}

		viewModel := c.usercase.ListUsers(input, page, c.config.Intranet.DefaultPageSize)
		errs := viewModel.Errors
		err := i.Render(w, r, "Admin/Users/<USER>", inertia.Props{
			"users":           viewModel.Data.Items,
			"usersPagination": viewModel.Data.Pagination,
			"roles":           viewModel.Data.Roles,
			"errors":          errs,
		})
		if err != nil {
			log.Printf("http error: %s\n", err)
			http.Error(w, "Internal Server Error", http.StatusInternalServerError)

			return
		}
	}

	return c.Render(fn)
}

func (c *Controller) UpdateRoleHandler() func(next http.Handler) http.Handler {
	i := c.inertia
	fn := func(w http.ResponseWriter, r *http.Request) {
		var err error
		userID := r.PathValue("userID")
		props := inertia.Props{}
		r.RequestURI = r.Referer()
		props["updateError"] = nil
		userUUID, err := uuid.Parse(userID)
		if err != nil {
			props["updateError"] = map[string]string{"id": userID, "message": err.Error()}
		}

		var input in.UpdateUserRoleInput
		if err := json.NewDecoder(r.Body).Decode(&input); err != nil {
			http.Error(w, "Invalid JSON", http.StatusBadRequest)
			return
		}

		viewModel := c.usercase.UpdateUserRole(userUUID, input)
		if !viewModel.Success {
			props["updateError"] = map[string]string{"id": userID, "message": viewModel.Errors[0].Message}
		}

		if viewModel.Success {
			// clear cache for user jwt token
			c.cache.Delete(r.Context(), "user_jwt_"+viewModel.Data.UserEmail)
		}

		err = i.Render(w, r, "Admin/Users/<USER>", props)
		if err != nil {
			log.Printf("http error: %s\n", err)
		}
	}
	return c.Render(fn)
}

func (c *Controller) Render(fn http.HandlerFunc) func(next http.Handler) http.Handler {
	i := c.inertia

	return func(next http.Handler) http.Handler {
		return i.Middleware(http.HandlerFunc(fn))
	}
}
