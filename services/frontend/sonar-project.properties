# Required metadata
sonar.organization=applaudo-devops
sonar.sca.enabled=true
sonar.projectKey=sa-intranet-frontend
sonar.projectName=SA Intranet Frontend
sonar.projectVersion=1.0

# Source code location
sonar.sources=.
sonar.exclusions=**/vendor/**,**/tmp/**,**/wiki/**,**/import_service/**,**/*_test.go,**/mock/**,**/mocks/**,**/testdata/**

# Test coverage configuration
sonar.tests=.
sonar.test.inclusions=**/*_test.go
sonar.test.exclusions=**/vendor/**,**/tmp/**,**/testdata/**
sonar.go.coverage.reportPaths=devops/coverage.out
sonar.coverage.exclusions=**/mock/**,**/mocks/**,**/cmd/**,**/main.go,**/testdata/**

# Go specific settings
sonar.go.file.suffixes=.go
sonar.go.gometalinter.reportPaths=libs/intranet/coverage/golangci-lint-report.xml
sonar.go.govet.reportPaths=libs/intranet/coverage/govet-report.out

# Encoding of source files
sonar.sourceEncoding=UTF-8

# Analysis settings
sonar.analysis.mode=publish
sonar.verbose=false

# Exclude generated files and test files from duplication detection
sonar.cpd.exclusions=**/*_test.go,**/mock/**,**/mocks/**,**/testdata/**

# Set the language
sonar.language=go

# Additional settings
sonar.scm.provider=git
sonar.scm.disabled=false

# Quality Gate settings
sonar.qualitygate.wait=true

# Remove the module settings as they're causing path resolution issues
# sonar.modules=libs,services
# libs.sonar.projectBaseDir=libs
# services.sonar.projectBaseDir=services

# Maintainability settings
sonar.duplications.exclusion.filters=**/*_test.go,**/mock/**,**/mocks/**
