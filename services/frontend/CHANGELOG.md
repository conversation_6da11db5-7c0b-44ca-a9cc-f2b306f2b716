# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.1.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

<a name="unreleased"></a>
## [Unreleased]


<a name="v1.7.0"></a>
## [v1.7.0] - 2025-10-03
### ⚙️ Miscellaneous Tasks 

- update Go version to 1.25.0 across project files and adjust related configurations 
- add node_modules to .gitignore and .aiignore 

### 📝 Documentation 

- update cqm-overview-prod dashboard SQL queries and layout for improved metrics visualization 
- update steerco-metrics dashboard with enhanced content and new metrics 
- add mandatory SteerCo Metrics links to Application and Business Architect onboarding sections 
- add mandatory training and documentation links for Application and Business Architects onboarding 
- add guidance for verifying Grafana permissions and submitting access requests 
- enhance SteerCo Metrics documentation with detailed instructions and structure 
- update SteerCo Metrics with detailed Excel and PPT instructions 
- update SteerCo Metrics with detailed Excel and PPT instructions\n- Added detailed section explaining how to fill the Architecture Metrics Template.xlsx including images for technical issues and repositories tabs.\n- Included step-by-step guidance on preparing the three key SteerCo presentation slides with embedded Hugo image shortcodes.\n- Clarified responsibilities and rotation for presenting architects.\n- Updated flow and troubleshooting sections for accuracy and completeness. 
- restructure Resources section in README.md for clarity 
- refine test coverage prompt with guidelines and templates 
- add prompt for improving test coverage in libs/intranet 
- add .augment-guidelines for AI code agent 
- add AI code agent guidelines 
- update readme 

### ✨ Features 

- add tests for CompanyClients, JiraProjects, and SonarProjects interactors 
- restructure metrics documentation for clarity and consistency 
- update SonarCloud configuration for project settings and paths 
- enable SCA in SonarCloud configuration 
- add metrics documentation and update penalty mapping in SonarCloud integration 
- ensure Secure flag is set for authentication session cookies in LogoutHandler 
- enable OSS Index disabling in dependency check script 
- Upgrade Go version, frontend dependencies to improve security 
- update security scan configurations and dependencies in Taskfile and Docker Compose 
- filter SonarQube issues by status in GetAllIssues function 
- implement SonarQube CLI tool for issue querying and report generation 
- enhance AWS Cognito policy with test mode support and certificate URL handling 
- improve test coverage 
- add SQL script for Documented Technical Risk Ratio (DTRR) analysis with enhanced timestamp parsing and metrics aggregation 
- add AI prompt template for SonarQube issues generation 
- update Go modules and dependencies across backend and frontend services 
- Implement AI prompt generation for SonarQube issues 
- update grid positions and version for steerco-metrics dashboard 
- update steerco-metrics dashboard with new SQL query and additional panel for SonarQube project trends 
- add new steerco-metrics dashboard configuration with enhanced data visualization 
- update cqm-overview-prod dashboard content and increment version to 22 
- enhance SteerCo Metrics documentation with detailed flow and action plans 
- add metrics section to Application Architect and Business Architect documentation; create SteerCo Metrics page 
- add JWT secret key default value and enhance token service tests 
- add JWT secret key validation and update error message for authentication 
- Update intranet tests and add gotest installation 
- add Complexity Level Catalog for JIRA to standardize task estimation 
- Integrate gotestsum for enhanced test reporting in intranet 
- Integrate security scanning and update CI/CD tasks 
- update JiraProject and SonarProject structures to use pointers for CompanyClient and JiraProject 
- add GetMeasures and validation tests for SonarQube client 
- add improve-test-coverage guidelines for enhancing test coverage in libs/intranet 
- create AGENT.md and agent instruction generator for build, architecture, and code style guidelines 
- add integration tests for CQM repositories and unit tests for repository implementations 
- enhance core libraries with tests and configuration options 
- add tests for ListUsers and UpdateUserRole in auth service 
- enhance documentation and structure for CQM and Auth use cases 
- add documentation and enhance core libraries 
- add http middlewares and tests, update Jira project creation and issue creation 
- update Complexity Level Catalog for JIRA to include architecture category and menu structure 
- add jira client to usecase/cqm 
- add core, config, http client, and validator libs with tests 
- add .idea to .gitignore 
- update go.work.sum dependencies 
- remove draft policies and processes pages from wiki 
- update README with additional tasks for security scans and deployment 
- Upgrade Go version to 1.24.4 across all modules 
- Update test coverage command to exclude additional packages and enhance sonar exclusions 
- **auth:** add development user authentication middleware and configuration 
- **auth:** implement token management functionality 
- **cqm:** :card_file_box: Add active to sonarqube projects 
- **cqm:** :card_file_box: add active to jira projects 
- **migrations:** add 'active' column to jira and sonarqube projects tables with index 
- **profile:** add profile and tokens management pages with token creation functionality 
- **tokens:** implement Token model, repository, and migration for token management 

### 🐛 Bug Fixes 

- update SQL query for SonarQube metrics and adjust time range settings 
- update button label to clarify navigation to SonarQube issues 
- update Go base image to 1.25-alpine and specify gocov version in Taskfile 
- handle NULL values in SonarQube analysis measures for improved data integrity 
- update datasource UID in cqm-overview-prod dashboard for consistency 
- add coverpkg option to gotestsum command for improved test coverage reporting 
- correct command in test coverage doc 
- remove intranet namespace from task command 
- update golangci-lint installation command 
- update opa rego import path to v1 
- update intranet dependencies 

### ♻️ Refactor 

- update golangci-lint configuration and task commands for improved linting process 
- update task commands and remove deprecated sonar-scanner preparation steps 
- update SQL views for SonarQube analysis and metrics, enhancing query structure and performance 
- improve error handling and modularize AI prompt generation in SonarQube analysis 
- streamline user ID setup in TestListTokens 
- refactor date handling in token services and presenters 
- remove unused Find and FindByJiraProjectID methods from Sonarqube repositories 
- remove outdated integration tests for CQM repositories 
- remove unused SonarqubeIssue repository methods 
- standardize error wrapping in SonarQube client 
- improve error handling and logging in SonarQube client 
- Remove unused Papyrus client implementation and tests 
- Improve logging in SonarQube and Jira mock servers 

### ✅ Testing 

- **auth:** add unit tests for token creation, deletion, and listing functionality 


<a name="v1.6.0"></a>
## [v1.6.0] - 2025-06-11
### 📝 Documentation 

- Add Copilot instructions for monorepo 

### ✨ Features 

- Refactor RBAC policy validation and improve SonarQube API handling 
- Add golangci-lint to mise 
- Comment out cyclomatic complexity check in golangci configuration 
- Refactor Jira project retrieval to use DefaultClient 
- Refactor Jira issue creation to use default client 
- Enhance error handling and client request management 
- Enhance CQM module with core repository integration and API error handling 
- Refactor Jira project update and SonarQube issue retrieval 
- Release v1.6.0 and enhance features across multiple modules 
- Enhance CQM module, core functionalities, and CLI user role management 
- **frontend:** Improve sidebar styling and add admin link 
- **frontend:** Implement basic admin index page 
- **frontend:** Integrate Font Awesome icons in admin index page 
- **frontend:** Integrate FontAwesome icons in Admin Index 
- **frontend:** Implement active link highlighting in sidebar 
- **frontend:** Close sidebar drawer on link click 
- **grafana:** Add CQM overview dashboard and trim whitespace from quality gate status 
- **grafana:** Update project dashboard with enhanced metrics display and remove obsolete SQL schema 
- **grafana:** Add Grafana dashboard prototype with SonarQube data integration 
- **intranet:** Add write and delete actions to ea-service policy 
- **intranet:** Implement user listing and role update functionalities 
- **intranet:** Integrate SonarQube scanning into CI pipeline 
- **rbac:** Grant write access to ea-service resources 
- **tests:** Add test suite for various packages with error logging refactor(coverage): Update coverage configuration and cleanup tasks 
- **wiki:** Enable Mermaid diagrams and improve code highlighting 
- **wiki:** Add critical score category to code quality metrics 
- **wiki:** Enhance Business Architect page with resources and formatting 
- **wiki:** Enhance Business Architect page with additional resources and templates 
- **wiki:** Add onboarding section and detail collaboration for Business Architects 
- **wiki:** Remove Best Practices and Technical Breakdown pages from Arch Handbook 
- **wiki:** Add Architects Metrics page to Architecture Handbook 

### 🐛 Bug Fixes 

- Improve cache testing and fix race conditions in tests 
- **frontend:** Use <a> tag for logout link to prevent React errors 
- **intranet:** Improve error handling, validation, and logging 
- **intranet:** Allow dynamic page size for user listing 
- **wiki:** Correct typo in Playlists section of Business Architect documentation 

### ♻️ Refactor 

- Enhance error handling with static errors and consistent formatting 
- Improve migration lock handling with reusable function 
- Simplify UpdateUserViewModel by aliasing CreateUserViewModel 
- Rename validator extension package and update usages 
- **intranet:** Refactor code structure for improved readability and maintainability 
- **intranet:** Improve code structure and update dependencies 
- **tests:** Update test configurations for improved reliability and clarity 


<a name="v1.5.0"></a>
## [v1.5.0] - 2025-05-13
### ✨ Features 

- Release v1.5.0 
- **auth:** Enhance user management and authorization 
- **auth:** Implement user provisioning service and integrate with middleware 
- **auth:** Implement User model and repository with CRUD operations 
- **auth:** Implement user provisioning with Cognito JWT data 
- **cache:** Implement DefaultCache with basic cache operations and tests 
- **core:** Implement generic repository functions for CRUD operations 
- **cqm:** Enhance SonarQube issue display with link to SonarQube, effort, author, and tags 
- **devops:** Integrate go test and golangci-lint into sonar scan 
- **frontend:** Implement CLI operator command for user role management 
- **frontend:** Refactor application setup with dependency injection and bootstrap 
- **intranet:** Enhance FindAll function with field selection and count control 
- **intranet:** Implement RBAC policies for admin and enterprise architect roles 
- **intranet:** Implement core cache functionality 
- **intranet:** Decouple config loading from intranet package 
- **intranet:** Enhance Save function with update field control 
- **intranet:** Add first name and last name to user provisioning from JWT 
- **intranet:** Refactor RBAC policy for enhanced flexibility and security 
- **intranet:** Implement user authorization middleware and integrate with frontend 
- **intranet:** Implement RBAC policy for business architect role 
- **rbac:** Enhance RBAC with deny rules, wildcard path matching, and improved validation 
- **rbac:** Implement RBAC policy and validation logic with JSON configuration 

### 🐛 Bug Fixes 

- **app:** Remove TODO comment regarding intranet config loading in production 
- **auth:** Add JWT expiration time validation with clock skew tolerance 
- **cqm:** Correctly process SonarQube analysis measures 
- **devops:** Enhance SonarQube integration and code coverage reporting 
- **devops:** Improve SonarQube analysis and test coverage 

### ♻️ Refactor 

- **intranet:** Rename UserProvisioningService to UserProvisioningInteractor and improve error handling 


<a name="v1.4.0"></a>
## [v1.4.0] - 2025-04-23
### 📝 Documentation 

- **changelog:** Update CHANGELOG.md for v1.4.0 release 
- **standards:** Add Git commits and Go style guides 

### ✨ Features 

- **changelog:** Enhance emoji support for commit types and improve formatting 
- **cqm:** Validate SonarQube webhook branch and add issue search endpoint 


<a name="v1.3.0"></a>
## [v1.3.0] - 2025-04-21
### 👷 CI 

- **changelog:** Add automatic CHANGELOG generation, based on conventional commits (https://www.conventionalcommits.org/) 

### 📝 Documentation 

- **changelog:** Update CHANGELOG.md 

### ✨ Features 

- **dependency:** Add git-chglog tool 
- **web:** Add SonarQube webhook 


<a name="v1.2.0"></a>
## [v1.2.0] - 2025-04-15

<a name="v1.1.0"></a>
## [v1.1.0] - 2025-04-10

<a name="v1.0.0"></a>
## v1.0.0 - 2025-04-10

[Unreleased]: /compare/HEAD..v1.7.0
[v1.7.0]: /compare/v1.7.0..v1.6.0
[v1.6.0]: /compare/v1.6.0..v1.5.0
[v1.5.0]: /compare/v1.5.0..v1.4.0
[v1.4.0]: /compare/v1.4.0..v1.3.0
[v1.3.0]: /compare/v1.3.0..v1.2.0
[v1.2.0]: /compare/v1.2.0..v1.1.0
[v1.1.0]: /compare/v1.1.0..v1.0.0
